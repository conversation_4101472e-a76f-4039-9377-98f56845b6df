console.log('Starting test server...');

// Test basic imports
import express from 'express';
console.log('✅ Express imported');

import dotenv from 'dotenv';
console.log('✅ Dotenv imported');

dotenv.config();
console.log('✅ Environment loaded');

// Test database import
import { initDatabase, testConnection } from './config/databaseSQLite.js';
console.log('✅ Database config imported');

// Test auth import
import { authenticateToken } from './middleware/authSQLite.js';
console.log('✅ Auth middleware imported');

try {

  console.log('All imports successful!');

  // Test database connection
  console.log('Testing database connection...');
  await initDatabase();
  const connected = await testConnection();

  if (connected) {
    console.log('✅ Database connection successful');
  } else {
    console.log('❌ Database connection failed');
  }

  // Test basic server
  const app = express();
  const PORT = process.env.PORT || 5000;

  app.get('/test', (req, res) => {
    res.json({ message: 'Test server working!' });
  });

  app.listen(PORT, () => {
    console.log(`✅ Test server running on port ${PORT}`);
    console.log(`Test URL: http://localhost:${PORT}/test`);
  });

} catch (error) {
  console.error('❌ Error:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}
