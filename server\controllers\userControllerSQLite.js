import User from '../models/UserSQLite.js';
import Role from '../models/RoleSQLite.js';
import { validationResult } from 'express-validator';

// الحصول على جميع المستخدمين
export const getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, role_id, is_active, search } = req.query;
    
    const filters = {};
    if (role_id) filters.role_id = role_id;
    if (is_active !== undefined) filters.is_active = is_active === 'true';
    if (search) filters.search = search;
    if (limit !== 'all') filters.limit = parseInt(limit);

    const users = await User.findAll(filters);
    const stats = await User.getStats();

    // إزالة كلمات المرور من النتائج
    const safeUsers = users.map(user => {
      const { password, ...safeUser } = user;
      return safeUser;
    });

    res.json({
      success: true,
      data: {
        users: safeUsers,
        stats: stats,
        pagination: {
          page: parseInt(page),
          limit: limit === 'all' ? users.length : parseInt(limit),
          total: users.length
        }
      }
    });

  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      message_ar: 'فشل في جلب المستخدمين'
    });
  }
};

// الحصول على مستخدم بالمعرف
export const getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    // إزالة كلمة المرور من النتيجة
    const { password, ...safeUser } = user;

    res.json({
      success: true,
      data: safeUser
    });

  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user',
      message_ar: 'فشل في جلب المستخدم'
    });
  }
};

// إنشاء مستخدم جديد
export const createUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { email, password, name, name_ar, role_id, avatar } = req.body;

    // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'Email already exists',
        message_ar: 'البريد الإلكتروني موجود بالفعل'
      });
    }

    // التحقق من وجود الدور
    const role = await Role.findById(role_id);
    if (!role) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role ID',
        message_ar: 'معرف الدور غير صالح'
      });
    }

    // إنشاء المستخدم
    const newUser = await User.create({
      email,
      password,
      name,
      name_ar,
      role_id,
      avatar: avatar || '👤'
    });

    // إزالة كلمة المرور من النتيجة
    const { password: _, ...safeUser } = newUser;

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      message_ar: 'تم إنشاء المستخدم بنجاح',
      data: safeUser
    });

  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create user',
      message_ar: 'فشل في إنشاء المستخدم'
    });
  }
};

// تحديث مستخدم
export const updateUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { name, name_ar, role_id, avatar, is_active, is_verified } = req.body;

    // التحقق من وجود المستخدم
    const existingUser = await User.findById(id);
    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    // التحقق من الدور إذا تم تمريره
    if (role_id) {
      const role = await Role.findById(role_id);
      if (!role) {
        return res.status(400).json({
          success: false,
          message: 'Invalid role ID',
          message_ar: 'معرف الدور غير صالح'
        });
      }
    }

    // إعداد البيانات للتحديث
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (name_ar !== undefined) updateData.name_ar = name_ar;
    if (role_id !== undefined) updateData.role_id = role_id;
    if (avatar !== undefined) updateData.avatar = avatar;
    if (is_active !== undefined) updateData.is_active = is_active;
    if (is_verified !== undefined) updateData.is_verified = is_verified;

    // تحديث المستخدم
    const updatedUser = await User.update(id, updateData);

    // إزالة كلمة المرور من النتيجة
    const { password, ...safeUser } = updatedUser;

    res.json({
      success: true,
      message: 'User updated successfully',
      message_ar: 'تم تحديث المستخدم بنجاح',
      data: safeUser
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user',
      message_ar: 'فشل في تحديث المستخدم'
    });
  }
};

// حذف مستخدم
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من وجود المستخدم
    const existingUser = await User.findById(id);
    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    // منع حذف المستخدم الحالي
    if (parseInt(id) === req.user.id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account',
        message_ar: 'لا يمكن حذف حسابك الخاص'
      });
    }

    // منع حذف المدير الأعلى الوحيد
    if (existingUser.role_name === 'super_admin') {
      const superAdminCount = await User.findAll({ role_id: existingUser.role_id });
      if (superAdminCount.length <= 1) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete the last super admin',
          message_ar: 'لا يمكن حذف المدير الأعلى الأخير'
        });
      }
    }

    // حذف المستخدم
    const deleted = await User.delete(id);

    if (!deleted) {
      return res.status(500).json({
        success: false,
        message: 'Failed to delete user',
        message_ar: 'فشل في حذف المستخدم'
      });
    }

    res.json({
      success: true,
      message: 'User deleted successfully',
      message_ar: 'تم حذف المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete user',
      message_ar: 'فشل في حذف المستخدم'
    });
  }
};

// تغيير كلمة مرور مستخدم (للمدراء)
export const changeUserPassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { newPassword } = req.body;

    // التحقق من وجود المستخدم
    const existingUser = await User.findById(id);
    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    // تحديث كلمة المرور
    await User.updatePassword(id, newPassword);

    res.json({
      success: true,
      message: 'Password updated successfully',
      message_ar: 'تم تحديث كلمة المرور بنجاح'
    });

  } catch (error) {
    console.error('Change user password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update password',
      message_ar: 'فشل في تحديث كلمة المرور'
    });
  }
};

// إحصائيات المستخدمين
export const getUserStats = async (req, res) => {
  try {
    const stats = await User.getStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user statistics',
      message_ar: 'فشل في جلب إحصائيات المستخدمين'
    });
  }
};
