// Simple test to verify the application is working
const puppeteer = require('puppeteer');

async function testApp() {
  try {
    const browser = await puppeteer.launch();
    const page = await browser.newPage();
    
    // Navigate to the application
    await page.goto('http://localhost:3000');
    
    // Wait for the main component to load
    await page.waitForSelector('[data-testid="security-dashboard"]', { timeout: 5000 });
    
    console.log('✅ Application loaded successfully');
    
    // Check if the header is present
    const header = await page.$('h1');
    if (header) {
      const headerText = await page.evaluate(el => el.textContent, header);
      console.log('✅ Header found:', headerText);
    }
    
    await browser.close();
    console.log('✅ Test completed successfully');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testApp();
