{"header": {"title": "Cyber Defense Center", "subtitle": "Real-time Security Operations Platform", "search": "Search incidents...", "reportIncident": "Report Incident"}, "navigation": {"overview": "Overview", "incidents": "Incidents", "analytics": "Analytics", "team": "Team", "admin": "Admin"}, "stats": {"threatLevel": "Threat Level", "activeIncidents": "Active Incidents", "criticalIssues": "Critical Issues", "avgResponse": "Avg Response", "teamOnline": "Team Online", "securityScore": "Security Score", "requiresAction": "Requires immediate action", "improvement": "improvement", "vsLastHour": "vs last hour", "critical": "Critical", "elevated": "Elevated", "normal": "Normal"}, "charts": {"incidentTrend": "Incident & Threat Trend", "severityDistribution": "Severity Distribution"}, "incidents": {"title": "Active Incidents", "all": "All", "critical": "Critical", "high": "High", "medium": "Medium", "low": "Low", "assignedTo": "Assigned to", "progress": "Progress", "actionsCompleted": "actions completed", "threatLevel": "Threat Level", "assetsAffected": "assets affected"}, "incidentForm": {"title": "Report New Security Incident", "titleEn": "Incident Title (English)", "titleAr": "العنوان (عربي)", "type": "Incident Type", "priority": "Priority Level", "assignTo": "Assign To", "description": "Description", "affectedAssets": "Affected Assets", "ipAddresses": "IP Addresses", "placeholders": {"title": "Enter incident title", "titleAr": "أد<PERSON>ل عنوان الحادث", "description": "Provide detailed description of the incident...", "assets": "Server names, systems (comma separated)", "ips": "Suspicious IPs (comma separated)"}, "types": {"authentication": "Authentication Attack", "phishing": "<PERSON><PERSON>", "malware": "Malware", "network": "Network Intrusion", "data": "Data Breach"}, "priorities": {"low": "Low", "medium": "Medium", "high": "High", "critical": "Critical"}, "selectTeamMember": "Select team member", "cancel": "Cancel", "submit": "Submit Incident"}, "incidentDetails": {"incidentId": "Incident ID", "threatAnalysis": "Threat Analysis", "attackOrigin": "Attack Origin", "incidentType": "Incident Type", "description": "Description", "affectedSystems": "Affected Systems & Assets", "systems": "Systems", "ipAddresses": "IP Addresses", "responseTimeline": "Response Timeline", "inProgress": "In progress", "exportReport": "Export Report", "viewLogs": "View Logs", "close": "Close"}, "status": {"active": "Active", "investigating": "Investigating", "contained": "Contained", "resolved": "Resolved"}, "notifications": {"incidentCreated": "New incident created successfully", "incidentUpdated": "Incident updated successfully", "error": "An error occurred. Please try again."}, "login": {"title": "Security Center", "subtitle": "Sign in to access the security management system", "email": "Email Address", "password": "Password", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "signIn": "Sign In", "demoAccounts": "Demo Accounts", "footer": "Security Incident Management System v1.0"}, "auth": {"logout": "Logout"}, "admin": {"title": "Admin Dashboard", "subtitle": "Manage users, roles, and system settings", "addUser": "Add User", "users": "Users", "roles": "Roles", "settings": "Settings", "searchUsers": "Search users...", "allRoles": "All Roles", "user": "User", "role": "Role", "status": "Status", "lastLogin": "Last Login", "actions": "Actions", "active": "Active", "inactive": "Inactive", "never": "Never", "deactivate": "Deactivate", "activate": "Activate", "edit": "Edit", "noUsers": "No users found", "systemRoles": "System Roles", "systemSettings": "System Settings", "settingsComingSoon": "System settings will be available in the next update."}}