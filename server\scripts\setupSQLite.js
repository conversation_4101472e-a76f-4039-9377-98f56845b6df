import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const setupSQLiteDatabase = async () => {
  console.log('🚀 Setting up SQLite Database...\n');

  try {
    // إنشاء قاعدة البيانات
    const dbPath = path.join(__dirname, '..', 'database', 'security_incidents.db');
    
    // إنشاء مجلد قاعدة البيانات إذا لم يكن موجود
    const fs = await import('fs');
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    const db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    console.log('✅ SQLite database created at:', dbPath);

    // إنشاء الجداول
    console.log('\n📋 Creating tables...');

    // جدول الأدوار
    await db.exec(`
      CREATE TABLE IF NOT EXISTS roles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(50) UNIQUE NOT NULL,
        name_ar VARCHAR(50) NOT NULL,
        description TEXT,
        description_ar TEXT,
        permissions TEXT, -- JSON as TEXT in SQLite
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Roles table created');

    // جدول المستخدمين
    await db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid VARCHAR(36) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        name_ar VARCHAR(255),
        avatar VARCHAR(255),
        role_id INTEGER,
        is_active BOOLEAN DEFAULT 1,
        is_verified BOOLEAN DEFAULT 0,
        last_login DATETIME,
        login_attempts INTEGER DEFAULT 0,
        locked_until DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE SET NULL
      )
    `);
    console.log('✅ Users table created');

    // جدول الجلسات
    await db.exec(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        token_hash VARCHAR(255) NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ User sessions table created');

    // جدول الحوادث الأمنية
    await db.exec(`
      CREATE TABLE IF NOT EXISTS security_incidents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        incident_id VARCHAR(50) UNIQUE NOT NULL,
        title VARCHAR(500) NOT NULL,
        title_ar VARCHAR(500),
        description TEXT,
        type VARCHAR(20) NOT NULL CHECK (type IN ('authentication', 'phishing', 'malware', 'network', 'data')),
        priority VARCHAR(10) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
        status VARCHAR(15) DEFAULT 'active' CHECK (status IN ('active', 'investigating', 'contained', 'resolved')),
        threat_level INTEGER DEFAULT 50,
        location VARCHAR(255),
        affected_assets TEXT, -- JSON as TEXT
        ip_addresses TEXT, -- JSON as TEXT
        assigned_to INTEGER,
        reported_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        resolved_at DATETIME,
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE SET NULL
      )
    `);
    console.log('✅ Security incidents table created');

    // جدول سجل النشاطات
    await db.exec(`
      CREATE TABLE IF NOT EXISTS activity_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action VARCHAR(100) NOT NULL,
        resource_type VARCHAR(50),
        resource_id INTEGER,
        details TEXT, -- JSON as TEXT
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);
    console.log('✅ Activity logs table created');

    // إنشاء الفهارس
    await db.exec(`
      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);
      CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at);
      CREATE INDEX IF NOT EXISTS idx_incidents_status ON security_incidents(status);
      CREATE INDEX IF NOT EXISTS idx_incidents_priority ON security_incidents(priority);
      CREATE INDEX IF NOT EXISTS idx_logs_user ON activity_logs(user_id);
      CREATE INDEX IF NOT EXISTS idx_logs_action ON activity_logs(action);
    `);
    console.log('✅ Indexes created');

    // إدخال البيانات الأولية
    console.log('\n👥 Creating initial data...');

    // إنشاء الأدوار
    const roles = [
      {
        name: 'super_admin',
        name_ar: 'المدير الأعلى',
        description: 'Full system access with all permissions',
        description_ar: 'صلاحية كاملة للنظام مع جميع الأذونات',
        permissions: JSON.stringify(['*'])
      },
      {
        name: 'admin',
        name_ar: 'مدير',
        description: 'Administrative access with user management',
        description_ar: 'صلاحية إدارية مع إدارة المستخدمين',
        permissions: JSON.stringify([
          'users.create', 'users.read', 'users.update',
          'incidents.create', 'incidents.read', 'incidents.update', 'incidents.delete',
          'reports.read', 'reports.export'
        ])
      },
      {
        name: 'security_analyst',
        name_ar: 'محلل أمني',
        description: 'Security analyst with incident management',
        description_ar: 'محلل أمني مع إدارة الحوادث',
        permissions: JSON.stringify([
          'incidents.create', 'incidents.read', 'incidents.update',
          'reports.read'
        ])
      },
      {
        name: 'viewer',
        name_ar: 'مشاهد',
        description: 'Read-only access to incidents and reports',
        description_ar: 'صلاحية قراءة فقط للحوادث والتقارير',
        permissions: JSON.stringify(['incidents.read', 'reports.read'])
      }
    ];

    for (const role of roles) {
      await db.run(
        'INSERT OR IGNORE INTO roles (name, name_ar, description, description_ar, permissions) VALUES (?, ?, ?, ?, ?)',
        [role.name, role.name_ar, role.description, role.description_ar, role.permissions]
      );
    }
    console.log('✅ Roles created');

    // إنشاء المدير الأعلى
    const superAdminRole = await db.get('SELECT id FROM roles WHERE name = ?', ['super_admin']);
    const hashedPassword = await bcrypt.hash('Admin@123456', 12);
    const adminUuid = uuidv4();

    await db.run(
      `INSERT OR IGNORE INTO users (uuid, email, password, name, name_ar, role_id, is_active, is_verified, avatar) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [adminUuid, '<EMAIL>', hashedPassword, 'System Administrator', 'مدير النظام', superAdminRole.id, 1, 1, '👨‍💼']
    );
    console.log('✅ Super admin created');

    // إنشاء مستخدمين تجريبيين
    const testUsers = [
      {
        email: '<EMAIL>',
        name: 'Sarah Ahmed',
        name_ar: 'سارة أحمد',
        role: 'admin',
        avatar: '👩‍💻'
      },
      {
        email: '<EMAIL>',
        name: 'Mohamed Ali',
        name_ar: 'محمد علي',
        role: 'security_analyst',
        avatar: '👨‍🔧'
      }
    ];

    for (const user of testUsers) {
      const role = await db.get('SELECT id FROM roles WHERE name = ?', [user.role]);
      const userUuid = uuidv4();
      const defaultPassword = await bcrypt.hash('Password@123', 12);

      await db.run(
        `INSERT OR IGNORE INTO users (uuid, email, password, name, name_ar, role_id, is_active, is_verified, avatar) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [userUuid, user.email, defaultPassword, user.name, user.name_ar, role.id, 1, 1, user.avatar]
      );
    }
    console.log('✅ Test users created');

    // إنشاء حوادث تجريبية
    const incidents = [
      {
        incident_id: 'INC-2024-001',
        title: 'Suspicious Login Attempts Detected',
        title_ar: 'رصد محاولات دخول مشبوهة',
        description: 'Multiple failed login attempts from unknown IP addresses',
        type: 'authentication',
        priority: 'critical',
        status: 'active',
        threat_level: 85,
        location: 'Russia',
        affected_assets: JSON.stringify(['Mail Server', 'Active Directory']),
        ip_addresses: JSON.stringify(['*************', '*********'])
      },
      {
        incident_id: 'INC-2024-002',
        title: 'Phishing Campaign Targeting Employees',
        title_ar: 'حملة تصيد تستهدف الموظفين',
        description: 'Sophisticated phishing emails detected',
        type: 'phishing',
        priority: 'high',
        status: 'investigating',
        threat_level: 70,
        location: 'Unknown',
        affected_assets: JSON.stringify(['Email System']),
        ip_addresses: JSON.stringify(['Unknown'])
      }
    ];

    const users = await db.all('SELECT id FROM users LIMIT 3');
    
    for (let i = 0; i < incidents.length; i++) {
      const incident = incidents[i];
      const assignedTo = users[i % users.length].id;
      const reportedBy = users[(i + 1) % users.length].id;

      await db.run(
        `INSERT OR IGNORE INTO security_incidents 
         (incident_id, title, title_ar, description, type, priority, status, threat_level, location, affected_assets, ip_addresses, assigned_to, reported_by) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          incident.incident_id, incident.title, incident.title_ar, incident.description,
          incident.type, incident.priority, incident.status, incident.threat_level,
          incident.location, incident.affected_assets, incident.ip_addresses,
          assignedTo, reportedBy
        ]
      );
    }
    console.log('✅ Sample incidents created');

    await db.close();

    // إنشاء ملف .env
    const envContent = `# Database Configuration (SQLite)
DB_TYPE=sqlite
DB_PATH=./database/security_incidents.db

# JWT Configuration
JWT_SECRET=security-incident-jwt-secret-key-${Date.now()}
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=5000
NODE_ENV=development

# Admin Account
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin@123456
ADMIN_NAME=System Administrator

# Security
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=15`;

    fs.writeFileSync('.env', envContent);
    console.log('✅ .env file created');

    console.log('\n🎉 SQLite Database Setup Complete!');
    console.log('\n📋 Login Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin@123456');
    
    console.log('\n👥 Test Users:');
    console.log('<EMAIL> / Password@123 (Admin)');
    console.log('<EMAIL> / Password@123 (Security Analyst)');
    
    console.log('\n🚀 Next Steps:');
    console.log('1. Start the server: npm run dev');
    console.log('2. Test the API: http://localhost:5000/health');
    console.log('3. Login with the admin credentials above');

    return true;

  } catch (error) {
    console.error('❌ SQLite setup error:', error.message);
    return false;
  }
};

setupSQLiteDatabase().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
});
