import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, 
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadarChart, 
  PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ComposedChart
} from 'recharts';
import { TrendingUp, TrendingDown, Activity, Shield, AlertTriangle, Calendar } from 'lucide-react';
import { motion } from 'framer-motion';

const AdvancedAnalytics = ({ darkMode, isRTL }) => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState('7d');

  // Enhanced data for analytics
  const weeklyTrends = [
    { day: 'Mon', incidents: 12, threats: 28, resolved: 10, response_time: 15 },
    { day: 'Tue', incidents: 8, threats: 22, resolved: 7, response_time: 12 },
    { day: 'Wed', incidents: 15, threats: 35, resolved: 12, response_time: 18 },
    { day: 'Thu', incidents: 6, threats: 18, resolved: 5, response_time: 10 },
    { day: 'Fri', incidents: 20, threats: 45, resolved: 16, response_time: 22 },
    { day: 'Sat', incidents: 4, threats: 12, resolved: 4, response_time: 8 },
    { day: 'Sun', incidents: 3, threats: 8, resolved: 3, response_time: 6 }
  ];

  const threatCategories = [
    { category: 'Malware', count: 45, severity: 85, trend: 12 },
    { category: 'Phishing', count: 32, severity: 70, trend: -5 },
    { category: 'DDoS', count: 18, severity: 60, trend: 8 },
    { category: 'Intrusion', count: 25, severity: 90, trend: 15 },
    { category: 'Data Breach', count: 8, severity: 95, trend: -2 }
  ];

  const securityMetrics = [
    { metric: 'Detection Rate', value: 95, fullMark: 100 },
    { metric: 'Response Time', value: 88, fullMark: 100 },
    { metric: 'Containment', value: 92, fullMark: 100 },
    { metric: 'Recovery', value: 85, fullMark: 100 },
    { metric: 'Prevention', value: 78, fullMark: 100 }
  ];

  const hourlyActivity = [
    { hour: '00', incidents: 2, threats: 5 },
    { hour: '02', incidents: 1, threats: 3 },
    { hour: '04', incidents: 1, threats: 2 },
    { hour: '06', incidents: 3, threats: 8 },
    { hour: '08', incidents: 8, threats: 15 },
    { hour: '10', incidents: 12, threats: 22 },
    { hour: '12', incidents: 15, threats: 28 },
    { hour: '14', incidents: 18, threats: 32 },
    { hour: '16', incidents: 22, threats: 38 },
    { hour: '18', incidents: 16, threats: 25 },
    { hour: '20', incidents: 8, threats: 12 },
    { hour: '22', incidents: 4, threats: 8 }
  ];

  const colors = {
    primary: '#3b82f6',
    secondary: '#ef4444',
    success: '#10b981',
    warning: '#f59e0b',
    purple: '#8b5cf6'
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className={`p-3 rounded-lg shadow-lg border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {label}
          </p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Advanced Security Analytics
        </h2>
        <div className="flex items-center gap-2">
          {['24h', '7d', '30d', '90d'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 ${
                timeRange === range
                  ? 'bg-blue-500 text-white'
                  : `${darkMode ? 'bg-gray-800 text-gray-400 hover:bg-gray-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[
          { title: 'Total Incidents', value: '156', change: '+12%', icon: AlertTriangle, color: 'text-red-500' },
          { title: 'Avg Resolution Time', value: '14m', change: '-8%', icon: Activity, color: 'text-blue-500' },
          { title: 'Security Score', value: '87%', change: '+5%', icon: Shield, color: 'text-green-500' },
          { title: 'Threat Detection', value: '95%', change: '+2%', icon: TrendingUp, color: 'text-purple-500' }
        ].map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-6 rounded-xl border ${darkMode ? 'bg-gray-900/50 border-gray-800' : 'bg-white border-gray-200'}`}
          >
            <div className="flex items-center justify-between mb-2">
              <metric.icon className={`w-5 h-5 ${metric.color}`} />
              <span className={`text-sm font-medium ${
                metric.change.startsWith('+') ? 'text-green-500' : 'text-red-500'
              }`}>
                {metric.change}
              </span>
            </div>
            <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {metric.value}
            </div>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {metric.title}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Incident Trends */}
        <div className={`p-6 rounded-xl border ${darkMode ? 'bg-gray-900/50 border-gray-800' : 'bg-white border-gray-200'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Weekly Incident Trends
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={weeklyTrends}>
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#e5e7eb'} />
              <XAxis dataKey="day" stroke={darkMode ? '#9ca3af' : '#6b7280'} />
              <YAxis stroke={darkMode ? '#9ca3af' : '#6b7280'} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="incidents" fill={colors.primary} radius={[4, 4, 0, 0]} />
              <Line type="monotone" dataKey="resolved" stroke={colors.success} strokeWidth={3} />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        {/* Security Metrics Radar */}
        <div className={`p-6 rounded-xl border ${darkMode ? 'bg-gray-900/50 border-gray-800' : 'bg-white border-gray-200'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Security Performance Radar
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={securityMetrics}>
              <PolarGrid stroke={darkMode ? '#374151' : '#e5e7eb'} />
              <PolarAngleAxis dataKey="metric" tick={{ fontSize: 12, fill: darkMode ? '#9ca3af' : '#6b7280' }} />
              <PolarRadiusAxis angle={90} domain={[0, 100]} tick={false} />
              <Radar
                name="Performance"
                dataKey="value"
                stroke={colors.primary}
                fill={colors.primary}
                fillOpacity={0.3}
                strokeWidth={2}
              />
            </RadarChart>
          </ResponsiveContainer>
        </div>

        {/* Threat Categories */}
        <div className={`p-6 rounded-xl border ${darkMode ? 'bg-gray-900/50 border-gray-800' : 'bg-white border-gray-200'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Threat Categories Analysis
          </h3>
          <div className="space-y-4">
            {threatCategories.map((threat, index) => (
              <div key={threat.category} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {threat.category}
                    </span>
                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {threat.count} incidents
                    </span>
                  </div>
                  <div className={`w-full h-2 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
                    <div
                      className="h-full rounded-full bg-gradient-to-r from-blue-500 to-purple-500"
                      style={{ width: `${threat.severity}%` }}
                    />
                  </div>
                </div>
                <div className={`ml-4 flex items-center gap-1 text-sm ${
                  threat.trend > 0 ? 'text-red-500' : 'text-green-500'
                }`}>
                  {threat.trend > 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                  {Math.abs(threat.trend)}%
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 24-Hour Activity Heatmap */}
        <div className={`p-6 rounded-xl border ${darkMode ? 'bg-gray-900/50 border-gray-800' : 'bg-white border-gray-200'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            24-Hour Activity Pattern
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={hourlyActivity}>
              <defs>
                <linearGradient id="activityGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={colors.secondary} stopOpacity={0.3}/>
                  <stop offset="95%" stopColor={colors.secondary} stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#e5e7eb'} />
              <XAxis dataKey="hour" stroke={darkMode ? '#9ca3af' : '#6b7280'} />
              <YAxis stroke={darkMode ? '#9ca3af' : '#6b7280'} />
              <Tooltip content={<CustomTooltip />} />
              <Area
                type="monotone"
                dataKey="threats"
                stroke={colors.secondary}
                fillOpacity={1}
                fill="url(#activityGradient)"
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default AdvancedAnalytics;
