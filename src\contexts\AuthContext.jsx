import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNotifications } from '../components/NotificationSystem';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const API_BASE_URL = 'http://localhost:5000/api';

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { addNotification } = useNotifications();

  // التحقق من وجود token في localStorage عند تحميل التطبيق
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      verifyToken(token);
    } else {
      setLoading(false);
    }
  }, []);

  // التحقق من صحة التوكن
  const verifyToken = async (token) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/verify-token`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // الحصول على بيانات المستخدم
        const profileResponse = await fetch(`${API_BASE_URL}/auth/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();
          setUser(profileData.data);
          setIsAuthenticated(true);
        }
      } else {
        // التوكن غير صالح
        localStorage.removeItem('auth_token');
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      localStorage.removeItem('auth_token');
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  // تسجيل الدخول
  const login = async (email, password) => {
    try {
      setLoading(true);
      
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (response.ok) {
        // حفظ التوكن
        localStorage.setItem('auth_token', data.data.token);
        
        // تعيين بيانات المستخدم
        setUser(data.data.user);
        setIsAuthenticated(true);

        // إشعار نجاح
        addNotification({
          type: 'success',
          title: data.message,
          message: `مرحباً ${data.data.user.name}`,
          duration: 3000
        });

        return { success: true, data: data.data };
      } else {
        // إشعار خطأ
        addNotification({
          type: 'error',
          title: data.message || 'Login failed',
          message: data.message_ar || 'فشل في تسجيل الدخول',
          duration: 5000
        });

        return { success: false, error: data.message };
      }
    } catch (error) {
      console.error('Login error:', error);
      
      addNotification({
        type: 'error',
        title: 'Connection Error',
        message: 'خطأ في الاتصال بالخادم',
        duration: 5000
      });

      return { success: false, error: 'Connection failed' };
    } finally {
      setLoading(false);
    }
  };

  // تسجيل الخروج
  const logout = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      
      if (token) {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // تنظيف البيانات المحلية
      localStorage.removeItem('auth_token');
      setUser(null);
      setIsAuthenticated(false);
      
      addNotification({
        type: 'info',
        title: 'Logged out',
        message: 'تم تسجيل الخروج بنجاح',
        duration: 3000
      });
    }
  };

  // تحديث الملف الشخصي
  const updateProfile = async (profileData) => {
    try {
      const token = localStorage.getItem('auth_token');
      
      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(profileData)
      });

      const data = await response.json();

      if (response.ok) {
        // تحديث بيانات المستخدم
        setUser(prev => ({ ...prev, ...data.data }));
        
        addNotification({
          type: 'success',
          title: data.message,
          message: data.message_ar,
          duration: 3000
        });

        return { success: true, data: data.data };
      } else {
        addNotification({
          type: 'error',
          title: data.message,
          message: data.message_ar,
          duration: 5000
        });

        return { success: false, error: data.message };
      }
    } catch (error) {
      console.error('Profile update error:', error);
      
      addNotification({
        type: 'error',
        title: 'Update failed',
        message: 'فشل في تحديث الملف الشخصي',
        duration: 5000
      });

      return { success: false, error: 'Update failed' };
    }
  };

  // تغيير كلمة المرور
  const changePassword = async (currentPassword, newPassword) => {
    try {
      const token = localStorage.getItem('auth_token');
      
      const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          currentPassword, 
          newPassword,
          confirmPassword: newPassword 
        })
      });

      const data = await response.json();

      if (response.ok) {
        addNotification({
          type: 'success',
          title: data.message,
          message: data.message_ar,
          duration: 3000
        });

        return { success: true };
      } else {
        addNotification({
          type: 'error',
          title: data.message,
          message: data.message_ar,
          duration: 5000
        });

        return { success: false, error: data.message };
      }
    } catch (error) {
      console.error('Password change error:', error);
      
      addNotification({
        type: 'error',
        title: 'Password change failed',
        message: 'فشل في تغيير كلمة المرور',
        duration: 5000
      });

      return { success: false, error: 'Password change failed' };
    }
  };

  // التحقق من الصلاحيات
  const hasPermission = (permission) => {
    if (!user || !user.role || !user.role.permissions) {
      return false;
    }
    
    return user.role.permissions.includes(permission) || user.role.permissions.includes('*');
  };

  // التحقق من الدور
  const hasRole = (role) => {
    if (!user || !user.role) {
      return false;
    }
    
    return user.role.name === role;
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    updateProfile,
    changePassword,
    hasPermission,
    hasRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
