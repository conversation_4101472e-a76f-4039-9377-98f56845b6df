import express from 'express';
import {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  changeUserPassword,
  getUserStats
} from '../controllers/userControllerSQLite.js';
import {
  validateUserCreation,
  validateUserUpdate,
  validateUserPasswordChange
} from '../validators/authValidators.js';
import {
  authenticateToken,
  requirePermission,
  requireRole,
  logActivity
} from '../middleware/authSQLite.js';

const router = express.Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticateToken);

// الحصول على إحصائيات المستخدمين
router.get('/stats', 
  requirePermission('users.read'), 
  getUserStats
);

// الحصول على جميع المستخدمين
router.get('/', 
  requirePermission('users.read'), 
  getAllUsers
);

// الحصول على مستخدم بالمعرف
router.get('/:id', 
  requirePermission('users.read'), 
  getUserById
);

// إنشاء مستخدم جديد
router.post('/', 
  requirePermission('users.create'),
  validateUserCreation,
  logActivity('user_create', 'user'),
  createUser
);

// تحديث مستخدم
router.put('/:id', 
  requirePermission('users.update'),
  validateUserUpdate,
  logActivity('user_update', 'user'),
  updateUser
);

// تغيير كلمة مرور مستخدم (للمدراء فقط)
router.put('/:id/password', 
  requireRole(['super_admin', 'admin']),
  validateUserPasswordChange,
  logActivity('user_password_change', 'user'),
  changeUserPassword
);

// حذف مستخدم
router.delete('/:id', 
  requirePermission('users.delete'),
  logActivity('user_delete', 'user'),
  deleteUser
);

export default router;
