# Security Incident Management System

A comprehensive, real-time security operations platform built with React 19, featuring advanced analytics, multi-language support, and modern UI components.

## 🚀 Features

### Core Functionality
- **Real-time Incident Tracking**: Monitor and manage security incidents with live updates
- **Advanced Analytics Dashboard**: Interactive charts and metrics for threat analysis
- **Multi-language Support**: Full Arabic and English localization with RTL support
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

### Enhanced Features (New)
- **🌐 Internationalization (i18n)**: Complete Arabic/English translation system
- **🔔 Real-time Notifications**: Toast notifications for incident updates and system alerts
- **📊 Advanced Analytics**: Enhanced data visualization with multiple chart types
- **🎨 Improved UI/UX**: Modern animations and transitions using Framer Motion
- **⚡ React Compiler Integration**: Optimized performance with React Compiler
- **🔄 Language Switcher**: Dynamic language switching with RTL layout support

## 🛠 Technology Stack

### Frontend
- **React 19**: Latest React with concurrent features
- **React Compiler**: Automatic optimization and memoization
- **TypeScript**: Type-safe development (configured)
- **Tailwind CSS**: Utility-first CSS framework
- **Framer Motion**: Advanced animations and transitions

### Internationalization
- **react-i18next**: Complete i18n solution
- **i18next**: Core internationalization framework
- **i18next-browser-languagedetector**: Automatic language detection

### Data Visualization
- **Recharts**: Responsive chart library
- **Lucide React**: Modern icon library

### Development Tools
- **Vite**: Fast build tool and dev server
- **ESLint**: Code linting with React Compiler plugin
- **PostCSS**: CSS processing
- **Autoprefixer**: CSS vendor prefixing

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd security-incident-management
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

## 🌍 Multi-language Support

The application supports both English and Arabic with complete RTL (Right-to-Left) layout support.

### Language Features
- **Dynamic Language Switching**: Change language without page reload
- **RTL Layout Support**: Proper Arabic text direction and layout
- **Localized Content**: All UI elements, notifications, and data are translated
- **Browser Language Detection**: Automatic language detection based on browser settings
- **Persistent Language Preference**: Language choice saved in localStorage

### Adding New Languages
1. Create a new translation file in `src/i18n/locales/`
2. Add the language to the `resources` object in `src/i18n/index.js`
3. Update the `languages` array in `src/contexts/LanguageContext.jsx`

## 📊 Analytics Dashboard

The enhanced analytics dashboard provides comprehensive security insights:

### Chart Types
- **Trend Analysis**: Line and area charts for incident patterns
- **Threat Distribution**: Pie charts for severity and type analysis
- **Performance Metrics**: Radar charts for security KPIs
- **Activity Heatmaps**: 24-hour activity pattern visualization
- **Comparative Analysis**: Combined charts for multi-metric views

### Key Metrics
- Threat level indicators with real-time updates
- Response time analytics
- Team performance tracking
- Security score monitoring
- Incident resolution rates

## 🔔 Notification System

Real-time notification system with multiple types and customization options:

### Notification Types
- **Success**: Incident creation, updates, resolutions
- **Error**: System errors, validation failures
- **Warning**: Security alerts, threshold breaches
- **Info**: General system information

### Features
- Auto-dismiss with configurable duration
- Manual dismiss option
- RTL support for Arabic notifications
- Smooth animations and transitions
- Queue management for multiple notifications

## 🎨 UI/UX Enhancements

### Design Improvements
- **Modern Glassmorphism**: Backdrop blur effects and transparency
- **Smooth Animations**: Framer Motion powered transitions
- **Interactive Elements**: Hover effects and micro-interactions
- **Responsive Grid**: Adaptive layouts for all screen sizes
- **Accessibility**: ARIA labels and keyboard navigation support

### Theme System
- **Dark Mode**: Eye-friendly dark theme with proper contrast
- **Light Mode**: Clean, professional light theme
- **System Preference**: Automatic theme detection
- **Persistent Choice**: Theme preference saved locally

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:
```env
VITE_APP_TITLE=Security Incident Management
VITE_DEFAULT_LANGUAGE=en
VITE_ENABLE_NOTIFICATIONS=true
```

### Customization
- **Colors**: Modify Tailwind config for custom color schemes
- **Animations**: Adjust Framer Motion settings in components
- **Languages**: Add new locales in the i18n configuration
- **Charts**: Customize Recharts themes and data sources

## 📱 Responsive Design

The application is fully responsive with breakpoints:
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+
- **Large Desktop**: 1440px+

## 🚀 Performance Optimizations

### React Compiler
- Automatic memoization of components and hooks
- Optimized re-rendering with dependency tracking
- Reduced bundle size through dead code elimination

### Code Splitting
- Lazy loading of analytics components
- Dynamic imports for language files
- Route-based code splitting (when routing is added)

## 🔒 Security Features

- **Input Validation**: Client-side validation for all forms
- **XSS Protection**: Sanitized user inputs and outputs
- **CSRF Protection**: Token-based request validation (backend integration)
- **Secure Headers**: Content Security Policy and other security headers

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run integration tests
npm run test:integration

# Run e2e tests
npm run test:e2e

# Generate coverage report
npm run test:coverage
```

## 📈 Future Enhancements

### Planned Features
- **Real-time WebSocket Integration**: Live incident updates
- **Advanced Filtering**: Complex query builder for incidents
- **Export Functionality**: PDF/Excel report generation
- **User Management**: Role-based access control
- **API Integration**: RESTful API for backend connectivity
- **Mobile App**: React Native companion app

### Technical Improvements
- **PWA Support**: Progressive Web App capabilities
- **Offline Mode**: Service worker for offline functionality
- **Performance Monitoring**: Real-time performance metrics
- **Error Tracking**: Comprehensive error logging and reporting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- React team for React 19 and React Compiler
- Tailwind CSS for the utility-first CSS framework
- Recharts for the excellent charting library
- i18next team for internationalization support
- Framer Motion for smooth animations
