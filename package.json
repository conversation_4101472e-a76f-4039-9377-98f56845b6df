{"name": "security-incident-management", "version": "1.0.0", "description": "Security Incident Management System", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "lucide-react": "^0.263.1", "recharts": "^2.8.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "vite": "^4.4.5", "babel-plugin-react-compiler": "beta", "eslint-plugin-react-compiler": "beta"}}