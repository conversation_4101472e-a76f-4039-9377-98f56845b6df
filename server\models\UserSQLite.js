import { executeQuery, getOne, getAll } from '../config/databaseSQLite.js';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

class User {
  constructor(data) {
    this.id = data.id;
    this.uuid = data.uuid;
    this.email = data.email;
    this.name = data.name;
    this.name_ar = data.name_ar;
    this.avatar = data.avatar;
    this.role_id = data.role_id;
    this.is_active = data.is_active;
    this.is_verified = data.is_verified;
    this.last_login = data.last_login;
    this.login_attempts = data.login_attempts;
    this.locked_until = data.locked_until;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    
    // إضافة بيانات الدور إذا كانت متوفرة
    this.role_name = data.role_name;
    this.role_name_ar = data.role_name_ar;
    this.permissions = data.permissions;
  }

  // إنشاء مستخدم جديد
  static async create(userData) {
    const { email, password, name, name_ar, role_id, avatar } = userData;
    
    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, parseInt(process.env.BCRYPT_ROUNDS));
    const uuid = uuidv4();

    const query = `
      INSERT INTO users (uuid, email, password, name, name_ar, role_id, avatar, is_active, is_verified)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(query, [
      uuid, email, hashedPassword, name, name_ar, role_id, avatar, 1, 1
    ]);

    return await User.findById(result.insertId);
  }

  // البحث عن مستخدم بالمعرف
  static async findById(id) {
    const query = `
      SELECT u.*, r.name as role_name, r.name_ar as role_name_ar, r.permissions
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?
    `;
    
    const result = await getOne(query, [id]);
    return result ? new User(result) : null;
  }

  // البحث عن مستخدم بالبريد الإلكتروني
  static async findByEmail(email) {
    const query = `
      SELECT u.*, r.name as role_name, r.name_ar as role_name_ar, r.permissions
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.email = ?
    `;
    
    const result = await getOne(query, [email]);
    return result ? result : null;
  }

  // البحث عن مستخدم بالـ UUID
  static async findByUuid(uuid) {
    const query = `
      SELECT u.*, r.name as role_name, r.name_ar as role_name_ar, r.permissions
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.uuid = ?
    `;
    
    const result = await getOne(query, [uuid]);
    return result ? new User(result) : null;
  }

  // الحصول على جميع المستخدمين
  static async findAll(filters = {}) {
    let query = `
      SELECT u.id, u.uuid, u.email, u.name, u.name_ar, u.avatar, u.is_active, 
             u.is_verified, u.last_login, u.created_at,
             r.name as role_name, r.name_ar as role_name_ar
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE 1=1
    `;
    
    const params = [];

    if (filters.role_id) {
      query += ' AND u.role_id = ?';
      params.push(filters.role_id);
    }

    if (filters.is_active !== undefined) {
      query += ' AND u.is_active = ?';
      params.push(filters.is_active ? 1 : 0);
    }

    if (filters.search) {
      query += ' AND (u.name LIKE ? OR u.name_ar LIKE ? OR u.email LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY u.created_at DESC';

    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(parseInt(filters.limit));
    }

    const results = await getAll(query, params);
    return results.map(user => new User(user));
  }

  // تحديث بيانات المستخدم
  static async update(id, updateData) {
    const allowedFields = ['name', 'name_ar', 'avatar', 'role_id', 'is_active', 'is_verified'];
    const updates = [];
    const params = [];

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = ?`);
        if (key === 'is_active' || key === 'is_verified') {
          params.push(updateData[key] ? 1 : 0);
        } else {
          params.push(updateData[key]);
        }
      }
    });

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    params.push(id);
    const query = `UPDATE users SET ${updates.join(', ')}, updated_at = datetime('now') WHERE id = ?`;
    
    await executeQuery(query, params);
    return await User.findById(id);
  }

  // تحديث كلمة المرور
  static async updatePassword(id, newPassword) {
    const hashedPassword = await bcrypt.hash(newPassword, parseInt(process.env.BCRYPT_ROUNDS));
    const query = 'UPDATE users SET password = ?, updated_at = datetime(\'now\') WHERE id = ?';
    
    await executeQuery(query, [hashedPassword, id]);
    return true;
  }

  // حذف مستخدم
  static async delete(id) {
    const query = 'DELETE FROM users WHERE id = ?';
    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // التحقق من كلمة المرور
  static async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // تحديث محاولات تسجيل الدخول
  static async updateLoginAttempts(email, attempts, lockUntil = null) {
    const query = 'UPDATE users SET login_attempts = ?, locked_until = ? WHERE email = ?';
    await executeQuery(query, [attempts, lockUntil, email]);
  }

  // تحديث آخر تسجيل دخول
  static async updateLastLogin(id) {
    const query = 'UPDATE users SET last_login = datetime(\'now\'), login_attempts = 0, locked_until = NULL WHERE id = ?';
    await executeQuery(query, [id]);
  }

  // التحقق من حالة القفل
  static isLocked(user) {
    if (!user.locked_until) return false;
    return new Date(user.locked_until) > new Date();
  }

  // إحصائيات المستخدمين
  static async getStats() {
    const queries = [
      'SELECT COUNT(*) as total FROM users',
      'SELECT COUNT(*) as active FROM users WHERE is_active = 1',
      'SELECT COUNT(*) as verified FROM users WHERE is_verified = 1',
      'SELECT COUNT(*) as locked FROM users WHERE locked_until > datetime(\'now\')'
    ];

    const results = await Promise.all(
      queries.map(query => getAll(query))
    );

    return {
      total: results[0][0].total,
      active: results[1][0].active,
      verified: results[2][0].verified,
      locked: results[3][0].locked
    };
  }
}

export default User;
