import { executeQuery, initDatabase } from '../config/databaseSQLite.js';
import bcrypt from 'bcryptjs';

const cleanDatabase = async () => {
  try {
    console.log('🧹 تنظيف قاعدة البيانات...');
    
    // تهيئة قاعدة البيانات
    await initDatabase();
    
    // حذف جميع البيانات التجريبية
    console.log('🗑️ حذف الحوادث الأمنية...');
    await executeQuery('DELETE FROM security_incidents');
    
    console.log('🗑️ حذف سجلات النشاطات...');
    await executeQuery('DELETE FROM activity_logs');
    
    console.log('🗑️ حذف جلسات المستخدمين...');
    await executeQuery('DELETE FROM user_sessions');
    
    console.log('🗑️ حذف المستخدمين التجريبيين...');
    await executeQuery('DELETE FROM users WHERE email != ?', ['<EMAIL>']);
    
    // إعادة تعيين معرفات التسلسل
    console.log('🔄 إعادة تعيين معرفات التسلسل...');
    await executeQuery('DELETE FROM sqlite_sequence WHERE name IN (?, ?, ?, ?)', 
      ['security_incidents', 'activity_logs', 'user_sessions', 'users']);
    
    // تحديث كلمة مرور المدير وصلاحياته
    console.log('👤 تحديث حساب المدير...');
    const adminPassword = 'Admin@123456';
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    
    await executeQuery(`
      UPDATE users 
      SET password = ?, 
          name = 'System Administrator',
          name_ar = 'مدير النظام',
          login_attempts = 0,
          locked_until = NULL,
          last_login = NULL
      WHERE email = ?
    `, [hashedPassword, '<EMAIL>']);
    
    // التأكد من صلاحيات المدير الأعلى
    await executeQuery(`
      UPDATE roles 
      SET permissions = ? 
      WHERE name = 'super_admin'
    `, [JSON.stringify(['*'])]);
    
    console.log('✅ تم تنظيف قاعدة البيانات بنجاح!');
    console.log('\n📋 معلومات تسجيل الدخول:');
    console.log('البريد الإلكتروني: <EMAIL>');
    console.log('كلمة المرور: Admin@123456');
    console.log('\n🎯 النظام جاهز للاستخدام مع بيانات فارغة');
    
  } catch (error) {
    console.error('❌ خطأ في تنظيف قاعدة البيانات:', error);
    process.exit(1);
  }
};

cleanDatabase();
