#!/usr/bin/env python3
import http.server
import socketserver
import os
import sys

# تغيير المجلد الحالي إلى مجلد dist
dist_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dist')
if os.path.exists(dist_path):
    os.chdir(dist_path)
else:
    print("❌ dist folder not found. Please run 'npm run build' first.")
    sys.exit(1)

PORT = 3000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()

    def do_GET(self):
        # إذا كان الطلب لملف غير موجود، أرسل index.html (SPA routing)
        if self.path != '/' and not os.path.exists(self.path[1:]):
            self.path = '/index.html'
        return super().do_GET()

try:
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🚀 Frontend server running on http://localhost:{PORT}")
        print(f"📁 Serving files from: {os.getcwd()}")
        print("Press Ctrl+C to stop the server")
        httpd.serve_forever()
except KeyboardInterrupt:
    print("\n🛑 Server stopped")
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
