import React, { createContext, useContext, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertTriangle, Info, AlertCircle, Bell } from 'lucide-react';

const NotificationContext = createContext();

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

const NotificationItem = ({ notification, onRemove, darkMode, isRTL }) => {
  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'info':
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getBgColor = () => {
    if (darkMode) {
      switch (notification.type) {
        case 'success':
          return 'bg-green-900/20 border-green-500/30';
        case 'error':
          return 'bg-red-900/20 border-red-500/30';
        case 'warning':
          return 'bg-amber-900/20 border-amber-500/30';
        case 'info':
        default:
          return 'bg-blue-900/20 border-blue-500/30';
      }
    } else {
      switch (notification.type) {
        case 'success':
          return 'bg-green-50 border-green-200';
        case 'error':
          return 'bg-red-50 border-red-200';
        case 'warning':
          return 'bg-amber-50 border-amber-200';
        case 'info':
        default:
          return 'bg-blue-50 border-blue-200';
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: isRTL ? -300 : 300, scale: 0.8 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: isRTL ? -300 : 300, scale: 0.8 }}
      transition={{ duration: 0.3, type: "spring", stiffness: 100 }}
      className={`flex items-start gap-3 p-4 rounded-lg border backdrop-blur-sm ${getBgColor()}`}
    >
      {getIcon()}
      <div className="flex-1 min-w-0">
        <h4 className={`font-medium text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          {notification.title}
        </h4>
        {notification.message && (
          <p className={`text-sm mt-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            {notification.message}
          </p>
        )}
      </div>
      <button
        onClick={() => onRemove(notification.id)}
        className={`p-1 rounded-md transition-colors ${
          darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-200'
        }`}
      >
        <X className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
      </button>
    </motion.div>
  );
};

export const NotificationProvider = ({ children, darkMode = false, isRTL = false }) => {
  const [notifications, setNotifications] = useState([]);

  const addNotification = useCallback((notification) => {
    const id = Date.now() + Math.random();
    const newNotification = {
      id,
      type: 'info',
      duration: 5000,
      ...notification
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto remove after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearAll
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      
      {/* Notification Container */}
      <div className={`fixed top-4 ${isRTL ? 'left-4' : 'right-4'} z-50 space-y-3 max-w-sm w-full`}>
        <AnimatePresence>
          {notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onRemove={removeNotification}
              darkMode={darkMode}
              isRTL={isRTL}
            />
          ))}
        </AnimatePresence>
      </div>
    </NotificationContext.Provider>
  );
};
