import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const officialLaunch = async () => {
  try {
    console.log('\n🚀 الإطلاق الرسمي لنظام إدارة الحوادث الأمنية');
    console.log('='.repeat(60));
    
    const dbPath = path.join(__dirname, '..', 'database', 'security_incidents.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    // التحقق من حالة النظام
    console.log('\n🔍 فحص حالة النظام...');
    
    // فحص الحوادث
    const incidents = await db.all('SELECT COUNT(*) as count FROM security_incidents');
    const incidentCount = incidents[0].count;
    
    // فحص المستخدمين
    const users = await db.all('SELECT COUNT(*) as count FROM users');
    const userCount = users[0].count;
    
    // فحص المدير
    const admin = await db.get('SELECT * FROM users WHERE email = ?', ['<EMAIL>']);
    
    // فحص الأدوار
    const roles = await db.all('SELECT COUNT(*) as count FROM roles');
    const roleCount = roles[0].count;

    await db.close();

    console.log('\n📊 تقرير حالة النظام:');
    console.log('─'.repeat(40));
    console.log(`✅ قاعدة البيانات: متصلة`);
    console.log(`✅ الحوادث الأمنية: ${incidentCount} (نظيف)`);
    console.log(`✅ المستخدمين: ${userCount} (المدير فقط)`);
    console.log(`✅ الأدوار: ${roleCount} (مكتملة)`);
    console.log(`✅ حساب المدير: ${admin ? 'موجود' : 'غير موجود'}`);

    if (incidentCount === 0 && userCount === 1 && admin) {
      console.log('\n🎉 النظام جاهز للإطلاق الرسمي!');
      console.log('─'.repeat(40));
      
      console.log('\n🔐 بيانات تسجيل الدخول:');
      console.log(`📧 البريد الإلكتروني: <EMAIL>`);
      console.log(`🔑 كلمة المرور: Admin@123456`);
      
      console.log('\n🌐 روابط النظام:');
      console.log(`🖥️  الواجهة الأمامية: http://localhost:3000`);
      console.log(`⚙️  الخادم الخلفي: http://localhost:5000`);
      console.log(`🏥 فحص الصحة: http://localhost:5000/health`);
      
      console.log('\n📋 الميزات المتاحة:');
      console.log('✅ إدارة الحوادث الأمنية (إنشاء، تعديل، حذف)');
      console.log('✅ إدارة المستخدمين والأدوار');
      console.log('✅ نظام صلاحيات متقدم');
      console.log('✅ تقارير وإحصائيات شاملة');
      console.log('✅ دعم اللغة العربية والإنجليزية');
      console.log('✅ واجهة مستخدم متجاوبة');
      console.log('✅ نظام إشعارات متقدم');
      
      console.log('\n🛡️ الأمان:');
      console.log('✅ تشفير كلمات المرور');
      console.log('✅ نظام JWT للمصادقة');
      console.log('✅ حماية من هجمات CSRF');
      console.log('✅ تسجيل جميع النشاطات');
      console.log('✅ نظام قفل الحسابات');
      
      console.log('\n📈 الخطوات التالية:');
      console.log('1️⃣  إنشاء المستخدمين الحقيقيين');
      console.log('2️⃣  تكوين إعدادات النظام');
      console.log('3️⃣  إضافة الحوادث الأمنية الحقيقية');
      console.log('4️⃣  تدريب الفريق على استخدام النظام');
      console.log('5️⃣  إعداد النسخ الاحتياطية');
      
      console.log('\n🎯 النظام جاهز للاستخدام الإنتاجي!');
      console.log('='.repeat(60));
      
      // إنشاء ملف تقرير الإطلاق
      const launchReport = `
# تقرير الإطلاق الرسمي لنظام إدارة الحوادث الأمنية

## معلومات الإطلاق
- **التاريخ**: ${new Date().toLocaleString('ar-EG')}
- **الإصدار**: 1.0.0
- **حالة النظام**: جاهز للإنتاج

## إحصائيات النظام
- **الحوادث**: ${incidentCount} (نظيف)
- **المستخدمين**: ${userCount} (المدير فقط)
- **الأدوار**: ${roleCount} (مكتملة)

## بيانات تسجيل الدخول
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Admin@123456

## الروابط
- **الواجهة الأمامية**: http://localhost:3000
- **الخادم الخلفي**: http://localhost:5000
- **فحص الصحة**: http://localhost:5000/health

## الميزات المتاحة
✅ إدارة الحوادث الأمنية
✅ إدارة المستخدمين والأدوار
✅ نظام صلاحيات متقدم
✅ تقارير وإحصائيات
✅ دعم متعدد اللغات
✅ واجهة متجاوبة
✅ نظام إشعارات

## الأمان
✅ تشفير كلمات المرور
✅ مصادقة JWT
✅ حماية CSRF
✅ تسجيل النشاطات
✅ قفل الحسابات

---
تم إنشاء هذا التقرير تلقائياً في ${new Date().toISOString()}
`;

      const fs = await import('fs');
      fs.writeFileSync('LAUNCH_REPORT.md', launchReport);
      console.log('\n📄 تم إنشاء تقرير الإطلاق: LAUNCH_REPORT.md');
      
    } else {
      console.log('\n❌ النظام غير جاهز للإطلاق!');
      console.log('يرجى تشغيل تنظيف قاعدة البيانات أولاً');
    }
    
  } catch (error) {
    console.error('❌ خطأ في فحص النظام:', error);
    process.exit(1);
  }
};

officialLaunch();
