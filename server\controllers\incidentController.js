import { executeQuery } from '../config/databaseSQLite.js';
import { validationResult } from 'express-validator';

// الحصول على جميع الحوادث
export const getAllIncidents = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, severity, search } = req.query;

    let query = `
      SELECT si.*, u.name as reporter_name, u.name_ar as reporter_name_ar
      FROM security_incidents si
      LEFT JOIN users u ON si.reported_by = u.id
      WHERE 1=1
    `;

    const params = [];

    if (status) {
      query += ' AND si.status = ?';
      params.push(status);
    }

    if (severity) {
      query += ' AND si.severity = ?';
      params.push(severity);
    }

    if (search) {
      query += ' AND (si.title LIKE ? OR si.title_ar LIKE ? OR si.description LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY si.created_at DESC';

    if (limit !== 'all') {
      const offset = (parseInt(page) - 1) * parseInt(limit);
      query += ' LIMIT ? OFFSET ?';
      params.push(parseInt(limit), offset);
    }

    const incidents = await executeQuery(query, params);

    // إحصائيات الحوادث
    const statsQuery = `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'open' THEN 1 END) as open,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed,
        COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical,
        COUNT(CASE WHEN severity = 'high' THEN 1 END) as high,
        COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium,
        COUNT(CASE WHEN severity = 'low' THEN 1 END) as low
      FROM security_incidents
    `;

    const stats = await executeQuery(statsQuery);

    res.json({
      success: true,
      data: {
        incidents,
        stats: stats[0],
        pagination: {
          page: parseInt(page),
          limit: limit === 'all' ? incidents.length : parseInt(limit),
          total: incidents.length
        }
      }
    });

  } catch (error) {
    console.error('Get all incidents error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incidents',
      message_ar: 'فشل في جلب الحوادث'
    });
  }
};

// الحصول على حادث بالمعرف
export const getIncidentById = async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT si.*,
             u1.name as reporter_name, u1.name_ar as reporter_name_ar,
             u2.name as assigned_name, u2.name_ar as assigned_name_ar
      FROM security_incidents si
      LEFT JOIN users u1 ON si.reported_by = u1.id
      LEFT JOIN users u2 ON si.assigned_to = u2.id
      WHERE si.id = ?
    `;

    const incidents = await executeQuery(query, [id]);

    if (incidents.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found',
        message_ar: 'الحادث غير موجود'
      });
    }

    res.json({
      success: true,
      data: incidents[0]
    });

  } catch (error) {
    console.error('Get incident by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incident',
      message_ar: 'فشل في جلب الحادث'
    });
  }
};

// إنشاء حادث جديد
export const createIncident = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const {
      title,
      title_ar,
      description,
      description_ar,
      severity,
      category,
      source_ip,
      target_system,
      assigned_to
    } = req.body;

    const query = `
      INSERT INTO security_incidents (
        title, title_ar, description, description_ar, severity, category,
        source_ip, target_system, status, reported_by, assigned_to
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', ?, ?)
    `;

    const result = await executeQuery(query, [
      title, title_ar, description, description_ar, severity, category,
      source_ip, target_system, req.user.id, assigned_to || null
    ]);

    // جلب الحادث المُنشأ
    const newIncident = await executeQuery(
      'SELECT * FROM security_incidents WHERE id = ?',
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: 'Incident created successfully',
      message_ar: 'تم إنشاء الحادث بنجاح',
      data: newIncident[0]
    });

  } catch (error) {
    console.error('Create incident error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create incident',
      message_ar: 'فشل في إنشاء الحادث'
    });
  }
};

// تحديث حادث
export const updateIncident = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const {
      title,
      title_ar,
      description,
      description_ar,
      severity,
      category,
      status,
      source_ip,
      target_system,
      assigned_to,
      resolution_notes,
      resolution_notes_ar
    } = req.body;

    // التحقق من وجود الحادث
    const existingIncident = await executeQuery(
      'SELECT * FROM security_incidents WHERE id = ?',
      [id]
    );

    if (existingIncident.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found',
        message_ar: 'الحادث غير موجود'
      });
    }

    // إعداد البيانات للتحديث
    const updates = [];
    const params = [];

    if (title !== undefined) {
      updates.push('title = ?');
      params.push(title);
    }
    if (title_ar !== undefined) {
      updates.push('title_ar = ?');
      params.push(title_ar);
    }
    if (description !== undefined) {
      updates.push('description = ?');
      params.push(description);
    }
    if (description_ar !== undefined) {
      updates.push('description_ar = ?');
      params.push(description_ar);
    }
    if (severity !== undefined) {
      updates.push('severity = ?');
      params.push(severity);
    }
    if (category !== undefined) {
      updates.push('category = ?');
      params.push(category);
    }
    if (status !== undefined) {
      updates.push('status = ?');
      params.push(status);

      // إذا تم حل الحادث، تحديث تاريخ الحل
      if (status === 'resolved' || status === 'closed') {
        updates.push('resolved_at = datetime(\'now\')');
      }
    }
    if (source_ip !== undefined) {
      updates.push('source_ip = ?');
      params.push(source_ip);
    }
    if (target_system !== undefined) {
      updates.push('target_system = ?');
      params.push(target_system);
    }
    if (assigned_to !== undefined) {
      updates.push('assigned_to = ?');
      params.push(assigned_to);
    }
    if (resolution_notes !== undefined) {
      updates.push('resolution_notes = ?');
      params.push(resolution_notes);
    }
    if (resolution_notes_ar !== undefined) {
      updates.push('resolution_notes_ar = ?');
      params.push(resolution_notes_ar);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update',
        message_ar: 'لا توجد حقول للتحديث'
      });
    }

    updates.push('updated_at = datetime(\'now\')');
    params.push(id);

    const query = `UPDATE security_incidents SET ${updates.join(', ')} WHERE id = ?`;
    await executeQuery(query, params);

    // جلب الحادث المُحدث
    const updatedIncident = await executeQuery(
      'SELECT * FROM security_incidents WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      message: 'Incident updated successfully',
      message_ar: 'تم تحديث الحادث بنجاح',
      data: updatedIncident[0]
    });

  } catch (error) {
    console.error('Update incident error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update incident',
      message_ar: 'فشل في تحديث الحادث'
    });
  }
};

// حذف حادث
export const deleteIncident = async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من وجود الحادث
    const existingIncident = await executeQuery(
      'SELECT * FROM security_incidents WHERE id = ?',
      [id]
    );

    if (existingIncident.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found',
        message_ar: 'الحادث غير موجود'
      });
    }

    // حذف الحادث
    await executeQuery('DELETE FROM security_incidents WHERE id = ?', [id]);

    res.json({
      success: true,
      message: 'Incident deleted successfully',
      message_ar: 'تم حذف الحادث بنجاح'
    });

  } catch (error) {
    console.error('Delete incident error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete incident',
      message_ar: 'فشل في حذف الحادث'
    });
  }
};

// إحصائيات الحوادث
export const getIncidentStats = async (req, res) => {
  try {
    const statsQuery = `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'open' THEN 1 END) as open,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed,
        COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical,
        COUNT(CASE WHEN severity = 'high' THEN 1 END) as high,
        COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium,
        COUNT(CASE WHEN severity = 'low' THEN 1 END) as low,
        COUNT(CASE WHEN created_at >= date('now', '-7 days') THEN 1 END) as last_week,
        COUNT(CASE WHEN created_at >= date('now', '-30 days') THEN 1 END) as last_month
      FROM security_incidents
    `;

    const stats = await executeQuery(statsQuery);

    res.json({
      success: true,
      data: stats[0]
    });

  } catch (error) {
    console.error('Get incident stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incident statistics',
      message_ar: 'فشل في جلب إحصائيات الحوادث'
    });
  }
};
