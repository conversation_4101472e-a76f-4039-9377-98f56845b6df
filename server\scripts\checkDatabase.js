import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const checkDatabase = async () => {
  try {
    console.log('🔍 فحص محتويات قاعدة البيانات...');

    const dbPath = path.join(__dirname, '..', 'database', 'security_incidents.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    console.log('\n=== فحص الحوادث ===');
    const incidents = await db.all('SELECT * FROM security_incidents');
    console.log(`عدد الحوادث: ${incidents.length}`);
    if (incidents.length > 0) {
      console.log('الحوادث الموجودة:');
      incidents.forEach(incident => {
        console.log(`- ${incident.id}: ${incident.title}`);
      });
    }

    console.log('\n=== فحص المستخدمين ===');
    const users = await db.all('SELECT * FROM users WHERE email != ?', ['<EMAIL>']);
    console.log(`عدد المستخدمين (غير المدير): ${users.length}`);
    if (users.length > 0) {
      console.log('المستخدمين الموجودين:');
      users.forEach(user => {
        console.log(`- ${user.id}: ${user.name} (${user.email})`);
      });
    }

    console.log('\n=== فحص سجلات النشاطات ===');
    const logs = await db.all('SELECT * FROM activity_logs');
    console.log(`عدد سجلات النشاطات: ${logs.length}`);

    console.log('\n=== فحص الجلسات ===');
    const sessions = await db.all('SELECT * FROM user_sessions');
    console.log(`عدد الجلسات: ${sessions.length}`);

    console.log('\n=== فحص الأدوار ===');
    const roles = await db.all('SELECT * FROM roles');
    console.log(`عدد الأدوار: ${roles.length}`);
    roles.forEach(role => {
      console.log(`- ${role.name}: ${role.description}`);
    });

    await db.close();

  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error);
  }
};

checkDatabase();
