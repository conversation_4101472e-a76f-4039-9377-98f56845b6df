import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const updatePermissions = async () => {
  try {
    console.log('🔧 تحديث صلاحيات الأدوار...');
    
    const dbPath = path.join(__dirname, '..', 'database', 'security_incidents.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    // صلاحيات المدير الأعلى - جميع الصلاحيات
    const superAdminPermissions = [
      '*', // صلاحية شاملة
      'users:create',
      'users:read',
      'users:update',
      'users:delete',
      'users:manage_permissions',
      'roles:create',
      'roles:read',
      'roles:update',
      'roles:delete',
      'incidents:create',
      'incidents:read',
      'incidents:update',
      'incidents:delete',
      'incidents:assign',
      'reports:create',
      'reports:read',
      'reports:export',
      'system:admin',
      'system:settings',
      'logs:read'
    ];

    // صلاحيات المدير
    const adminPermissions = [
      'users:create',
      'users:read',
      'users:update',
      'users:delete',
      'roles:read',
      'incidents:create',
      'incidents:read',
      'incidents:update',
      'incidents:delete',
      'incidents:assign',
      'reports:create',
      'reports:read',
      'reports:export',
      'logs:read'
    ];

    // صلاحيات المحلل الأمني
    const analystPermissions = [
      'incidents:create',
      'incidents:read',
      'incidents:update',
      'reports:create',
      'reports:read'
    ];

    // صلاحيات المستخدم العادي
    const userPermissions = [
      'incidents:create',
      'incidents:read'
    ];

    // تحديث الصلاحيات
    await db.run(`
      UPDATE roles 
      SET permissions = ? 
      WHERE name = 'super_admin'
    `, [JSON.stringify(superAdminPermissions)]);

    await db.run(`
      UPDATE roles 
      SET permissions = ? 
      WHERE name = 'admin'
    `, [JSON.stringify(adminPermissions)]);

    await db.run(`
      UPDATE roles 
      SET permissions = ? 
      WHERE name = 'security_analyst'
    `, [JSON.stringify(analystPermissions)]);

    await db.run(`
      UPDATE roles 
      SET permissions = ? 
      WHERE name = 'user'
    `, [JSON.stringify(userPermissions)]);

    await db.close();
    
    console.log('✅ تم تحديث صلاحيات الأدوار بنجاح!');
    console.log('\n📋 الصلاحيات المحدثة:');
    console.log('🔹 المدير الأعلى: جميع الصلاحيات');
    console.log('🔹 المدير: إدارة المستخدمين والحوادث');
    console.log('🔹 المحلل الأمني: إدارة الحوادث والتقارير');
    console.log('🔹 المستخدم: إنشاء وقراءة الحوادث');
    
  } catch (error) {
    console.error('❌ خطأ في تحديث الصلاحيات:', error);
    process.exit(1);
  }
};

updatePermissions();
