import { executeQuery, getOne, getAll } from '../config/databaseSQLite.js';

class Role {
  constructor(data) {
    this.id = data.id;
    this.name = data.name;
    this.name_ar = data.name_ar;
    this.description = data.description;
    this.description_ar = data.description_ar;
    this.permissions = typeof data.permissions === 'string' 
      ? JSON.parse(data.permissions) 
      : data.permissions;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // إنشاء دور جديد
  static async create(roleData) {
    const { name, name_ar, description, description_ar, permissions } = roleData;
    
    const query = `
      INSERT INTO roles (name, name_ar, description, description_ar, permissions)
      VALUES (?, ?, ?, ?, ?)
    `;

    const permissionsJson = JSON.stringify(permissions);
    const result = await executeQuery(query, [
      name, name_ar, description, description_ar, permissionsJson
    ]);

    return await Role.findById(result.insertId);
  }

  // البحث عن دور بالمعرف
  static async findById(id) {
    const query = 'SELECT * FROM roles WHERE id = ?';
    const result = await getOne(query, [id]);
    return result ? new Role(result) : null;
  }

  // البحث عن دور بالاسم
  static async findByName(name) {
    const query = 'SELECT * FROM roles WHERE name = ?';
    const result = await getOne(query, [name]);
    return result ? new Role(result) : null;
  }

  // الحصول على جميع الأدوار
  static async findAll() {
    const query = `
      SELECT r.*, COUNT(u.id) as user_count
      FROM roles r
      LEFT JOIN users u ON r.id = u.role_id
      GROUP BY r.id
      ORDER BY r.created_at ASC
    `;
    
    const results = await getAll(query);
    return results.map(role => ({
      ...new Role(role),
      user_count: role.user_count
    }));
  }

  // تحديث دور
  static async update(id, updateData) {
    const allowedFields = ['name', 'name_ar', 'description', 'description_ar', 'permissions'];
    const updates = [];
    const params = [];

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        if (key === 'permissions') {
          updates.push(`${key} = ?`);
          params.push(JSON.stringify(updateData[key]));
        } else {
          updates.push(`${key} = ?`);
          params.push(updateData[key]);
        }
      }
    });

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    params.push(id);
    const query = `UPDATE roles SET ${updates.join(', ')}, updated_at = datetime('now') WHERE id = ?`;
    
    await executeQuery(query, params);
    return await Role.findById(id);
  }

  // حذف دور
  static async delete(id) {
    // التحقق من وجود مستخدمين مرتبطين بهذا الدور
    const userCount = await getOne('SELECT COUNT(*) as count FROM users WHERE role_id = ?', [id]);
    
    if (userCount.count > 0) {
      throw new Error('Cannot delete role with associated users');
    }

    const query = 'DELETE FROM roles WHERE id = ?';
    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // التحقق من الصلاحية
  static hasPermission(userPermissions, requiredPermission) {
    if (!userPermissions || !Array.isArray(userPermissions)) {
      return false;
    }
    
    return userPermissions.includes(requiredPermission) || userPermissions.includes('*');
  }

  // الحصول على جميع الصلاحيات المتاحة
  static getAvailablePermissions() {
    return {
      users: [
        'users.create',
        'users.read', 
        'users.update',
        'users.delete'
      ],
      roles: [
        'roles.create',
        'roles.read',
        'roles.update', 
        'roles.delete'
      ],
      incidents: [
        'incidents.create',
        'incidents.read',
        'incidents.update',
        'incidents.delete',
        'incidents.assign'
      ],
      reports: [
        'reports.read',
        'reports.export',
        'reports.analytics'
      ],
      system: [
        'system.settings',
        'system.logs',
        'system.backup'
      ]
    };
  }

  // التحقق من صحة الصلاحيات
  static validatePermissions(permissions) {
    const availablePermissions = Role.getAvailablePermissions();
    const allPermissions = Object.values(availablePermissions).flat();
    
    for (const permission of permissions) {
      if (permission !== '*' && !allPermissions.includes(permission)) {
        return false;
      }
    }
    
    return true;
  }
}

export default Role;
