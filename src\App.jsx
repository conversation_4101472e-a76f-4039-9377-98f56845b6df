import React, { useState } from 'react';
import SecurityIncidentManagementContent from './SecurityIncidentManagement';
import LoginForm from './components/LoginForm';
import { LanguageProvider } from './contexts/LanguageContext';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { NotificationProvider } from './components/NotificationSystem';
import { useLanguage } from './contexts/LanguageContext';

// مكون داخلي للتحكم في عرض المحتوى
const AppContent = () => {
  const { isAuthenticated, loading } = useAuth();
  const { isRTL } = useLanguage();
  const [darkMode, setDarkMode] = useState(true);

  if (loading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${
        darkMode ? 'bg-gray-900' : 'bg-gray-50'
      }`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Loading...
          </p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <NotificationProvider darkMode={darkMode} isRTL={isRTL}>
        <LoginForm darkMode={darkMode} setDarkMode={setDarkMode} />
      </NotificationProvider>
    );
  }

  return (
    <NotificationProvider darkMode={darkMode} isRTL={isRTL}>
      <SecurityIncidentManagementContent />
    </NotificationProvider>
  );
};

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <div className="App">
          <AppContent />
        </div>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;
