import { body } from 'express-validator';

// التحقق من صحة بيانات إنشاء حادث جديد
export const validateIncidentCreation = [
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),

  body('title_ar')
    .optional()
    .isLength({ min: 3, max: 255 })
    .withMessage('Arabic title must be between 3 and 255 characters'),

  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),

  body('description_ar')
    .optional()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Arabic description must be between 10 and 2000 characters'),

  body('severity')
    .notEmpty()
    .withMessage('Severity is required')
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Severity must be one of: low, medium, high, critical'),

  body('category')
    .notEmpty()
    .withMessage('Category is required')
    .isIn(['malware', 'phishing', 'data_breach', 'unauthorized_access', 'ddos', 'insider_threat', 'other'])
    .withMessage('Invalid category'),

  body('source_ip')
    .optional()
    .isIP()
    .withMessage('Source IP must be a valid IP address'),

  body('target_system')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Target system must not exceed 255 characters'),

  body('assigned_to')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Assigned to must be a valid user ID')
];

// التحقق من صحة بيانات تحديث حادث
export const validateIncidentUpdate = [
  body('title')
    .optional()
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),

  body('title_ar')
    .optional()
    .isLength({ min: 3, max: 255 })
    .withMessage('Arabic title must be between 3 and 255 characters'),

  body('description')
    .optional()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),

  body('description_ar')
    .optional()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Arabic description must be between 10 and 2000 characters'),

  body('severity')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Severity must be one of: low, medium, high, critical'),

  body('category')
    .optional()
    .isIn(['malware', 'phishing', 'data_breach', 'unauthorized_access', 'ddos', 'insider_threat', 'other'])
    .withMessage('Invalid category'),

  body('status')
    .optional()
    .isIn(['open', 'in_progress', 'resolved', 'closed'])
    .withMessage('Status must be one of: open, in_progress, resolved, closed'),

  body('source_ip')
    .optional()
    .isIP()
    .withMessage('Source IP must be a valid IP address'),

  body('target_system')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Target system must not exceed 255 characters'),

  body('assigned_to')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Assigned to must be a valid user ID'),

  body('resolution_notes')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Resolution notes must not exceed 2000 characters'),

  body('resolution_notes_ar')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Arabic resolution notes must not exceed 2000 characters')
];
