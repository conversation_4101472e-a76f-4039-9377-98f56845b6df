{"name": "security-incident-backend", "version": "1.0.0", "description": "Backend for Security Incident Management System", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "start-sqlite": "node serverSQLite.js", "dev": "nodemon server.js", "dev-sqlite": "nodemon serverSQLite.js", "setup-db": "node scripts/setupDatabase.js", "seed-db": "node scripts/seedDatabase.js", "quick-setup": "node scripts/quickSetup.js", "setup-sqlite": "node scripts/setupSQLite.js", "check-mysql": "node scripts/installMySQL.js", "clean-db": "node scripts/quickClean.js", "check-db": "node scripts/checkDatabase.js", "launch": "node scripts/officialLaunch.js", "update-permissions": "node scripts/updatePermissions.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.6.5", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["security", "incident", "management", "mysql", "authentication"], "author": "Security Team", "license": "MIT"}