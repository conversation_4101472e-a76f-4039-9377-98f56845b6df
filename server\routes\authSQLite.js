import express from 'express';
import {
  login,
  logout,
  logoutAll,
  getProfile,
  updateProfile,
  changePassword,
  verifyToken
} from '../controllers/authControllerSQLite.js';
import {
  validateLogin,
  validateProfileUpdate,
  validatePasswordChange
} from '../validators/authValidators.js';
import { authenticateToken, logActivity } from '../middleware/authSQLite.js';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting للحماية من هجمات Brute Force
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 5, // 5 محاولات كحد أقصى
  message: {
    success: false,
    message: 'Too many login attempts, please try again later',
    message_ar: 'محاولات دخول كثيرة جداً، يرجى المحاولة لاحقاً'
  },
  standardHeaders: true,
  legacyHeaders: false
});

const passwordChangeLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // ساعة واحدة
  max: 3, // 3 محاولات كحد أقصى
  message: {
    success: false,
    message: 'Too many password change attempts, please try again later',
    message_ar: 'محاولات تغيير كلمة مرور كثيرة جداً، يرجى المحاولة لاحقاً'
  }
});

// المسارات العامة (بدون مصادقة)
router.post('/login', loginLimiter, validateLogin, logActivity('user_login'), login);
router.post('/verify-token', authenticateToken, verifyToken);

// المسارات المحمية (تتطلب مصادقة)
router.use(authenticateToken); // جميع المسارات التالية تتطلب مصادقة

router.post('/logout', logActivity('user_logout'), logout);
router.post('/logout-all', logActivity('user_logout_all'), logoutAll);
router.get('/profile', getProfile);
router.put('/profile', validateProfileUpdate, logActivity('profile_update'), updateProfile);
router.put('/change-password', passwordChangeLimiter, validatePasswordChange, logActivity('password_change'), changePassword);

export default router;
