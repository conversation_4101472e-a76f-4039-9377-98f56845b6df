import express from 'express';
import {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getAvailablePermissions
} from '../controllers/roleControllerSQLite.js';
import {
  validateRoleCreation,
  validateRoleUpdate
} from '../validators/roleValidators.js';
import {
  authenticateToken,
  requirePermission,
  logActivity
} from '../middleware/authSQLite.js';

const router = express.Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticateToken);

// الحصول على الصلاحيات المتاحة
router.get('/permissions', 
  requirePermission('roles.read'), 
  getAvailablePermissions
);

// الحصول على جميع الأدوار
router.get('/', 
  requirePermission('roles.read'), 
  getAllRoles
);

// الحصول على دور بالمعرف
router.get('/:id', 
  requirePermission('roles.read'), 
  getRoleById
);

// إنشاء دور جديد
router.post('/', 
  requirePermission('roles.create'),
  validateRoleCreation,
  logActivity('role_create', 'role'),
  createRole
);

// تحديث دور
router.put('/:id', 
  requirePermission('roles.update'),
  validateRoleUpdate,
  logActivity('role_update', 'role'),
  updateRole
);

// حذف دور
router.delete('/:id', 
  requirePermission('roles.delete'),
  logActivity('role_delete', 'role'),
  deleteRole
);

export default router;
