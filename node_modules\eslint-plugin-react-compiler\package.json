{"name": "eslint-plugin-react-compiler", "version": "19.0.0-beta-af1b7da-20250417", "description": "ESLint plugin to display errors found by the React compiler.", "main": "dist/index.js", "scripts": {"build": "rimraf dist && tsup", "test": "jest", "watch": "yarn build --watch"}, "files": ["dist"], "dependencies": {"@babel/core": "^7.24.4", "@babel/parser": "^7.24.4", "@babel/plugin-proposal-private-methods": "^7.18.6", "hermes-parser": "^0.25.1", "zod": "^3.22.4", "zod-validation-error": "^3.0.3"}, "devDependencies": {"@babel/preset-env": "^7.22.4", "@babel/preset-typescript": "^7.18.6", "@babel/types": "^7.26.0", "@types/eslint": "^8.56.12", "@types/node": "^20.2.5", "babel-jest": "^29.0.3", "eslint": "8.57.0", "hermes-eslint": "^0.25.1", "jest": "^29.5.0"}, "engines": {"node": "^14.17.0 || ^16.0.0 || >= 18.0.0"}, "peerDependencies": {"eslint": ">=7"}, "repository": {"type": "git", "url": "git+https://github.com/facebook/react.git", "directory": "compiler/packages/eslint-plugin-react-compiler"}, "license": "MIT"}