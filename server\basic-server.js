const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// إعداد CORS
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json());

// مسار الصحة
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// مسار تسجيل الدخول
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'Admin@123456') {
    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: 1,
          email: '<EMAIL>',
          name: 'System Administrator',
          name_ar: 'مدير النظام',
          avatar: '👨‍💼',
          role: {
            name: 'super_admin',
            name_ar: 'المدير الأعلى',
            permissions: ['*']
          }
        },
        token: 'fake-token',
        expires_in: '24h'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// مسار التحقق من التوكن
app.post('/api/auth/verify-token', (req, res) => {
  res.json({ success: true });
});

// مسار الملف الشخصي
app.get('/api/auth/profile', (req, res) => {
  res.json({
    success: true,
    data: {
      id: 1,
      email: '<EMAIL>',
      name: 'System Administrator',
      name_ar: 'مدير النظام',
      avatar: '👨‍💼',
      role: {
        name: 'super_admin',
        name_ar: 'المدير الأعلى',
        permissions: ['*']
      }
    }
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📊 Health: http://localhost:${PORT}/health`);
});
