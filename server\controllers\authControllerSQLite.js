import jwt from 'jsonwebtoken';
import User from '../models/UserSQLite.js';
import { executeQuery, getOne } from '../config/databaseSQLite.js';
import { validationResult } from 'express-validator';

// تسجيل الدخول
export const login = async (req, res) => {
  try {
    // التحقق من صحة البيانات المدخلة
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // البحث عن المستخدم
    const user = await User.findByEmail(email);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password',
        message_ar: 'بريد إلكتروني أو كلمة مرور غير صحيحة'
      });
    }

    // التحقق من حالة الحساب
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated',
        message_ar: 'الحساب معطل'
      });
    }

    // التحقق من قفل الحساب
    if (User.isLocked(user)) {
      const lockTime = new Date(user.locked_until).toLocaleString();
      return res.status(423).json({
        success: false,
        message: `Account is locked until ${lockTime}`,
        message_ar: `الحساب مقفل حتى ${lockTime}`,
        locked_until: user.locked_until
      });
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await User.verifyPassword(password, user.password);
    
    if (!isPasswordValid) {
      // زيادة عدد محاولات تسجيل الدخول الفاشلة
      const newAttempts = (user.login_attempts || 0) + 1;
      const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
      const lockoutTime = parseInt(process.env.LOCKOUT_TIME) || 15;

      let lockUntil = null;
      if (newAttempts >= maxAttempts) {
        const lockDate = new Date(Date.now() + lockoutTime * 60 * 1000);
        lockUntil = lockDate.toISOString();
      }

      await User.updateLoginAttempts(email, newAttempts, lockUntil);

      return res.status(401).json({
        success: false,
        message: 'Invalid email or password',
        message_ar: 'بريد إلكتروني أو كلمة مرور غير صحيحة',
        attempts_remaining: Math.max(0, maxAttempts - newAttempts)
      });
    }

    // إنشاء JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role_name
    };

    const token = jwt.sign(tokenPayload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN
    });

    // حفظ الجلسة في قاعدة البيانات
    const tokenHash = Buffer.from(token).toString('base64');
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 24 ساعة
    
    await executeQuery(
      'INSERT INTO user_sessions (user_id, token_hash, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, ?)',
      [user.id, tokenHash, req.ip, req.get('User-Agent'), expiresAt]
    );

    // تحديث آخر تسجيل دخول وإعادة تعيين محاولات الدخول
    await User.updateLastLogin(user.id);

    // إعداد بيانات المستخدم للإرسال (بدون كلمة المرور)
    const userData = {
      id: user.id,
      uuid: user.uuid,
      email: user.email,
      name: user.name,
      name_ar: user.name_ar,
      avatar: user.avatar,
      role: {
        id: user.role_id,
        name: user.role_name,
        name_ar: user.role_name_ar,
        permissions: user.permissions ? JSON.parse(user.permissions) : []
      },
      is_verified: user.is_verified,
      last_login: user.last_login
    };

    res.json({
      success: true,
      message: 'Login successful',
      message_ar: 'تم تسجيل الدخول بنجاح',
      data: {
        user: userData,
        token: token,
        expires_in: process.env.JWT_EXPIRES_IN
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      message_ar: 'فشل في تسجيل الدخول',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تسجيل الخروج
export const logout = async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const tokenHash = Buffer.from(token).toString('base64');
      
      // حذف الجلسة من قاعدة البيانات
      await executeQuery(
        'DELETE FROM user_sessions WHERE user_id = ? AND token_hash = ?',
        [req.user.id, tokenHash]
      );
    }

    res.json({
      success: true,
      message: 'Logout successful',
      message_ar: 'تم تسجيل الخروج بنجاح'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed',
      message_ar: 'فشل في تسجيل الخروج'
    });
  }
};

// تسجيل الخروج من جميع الأجهزة
export const logoutAll = async (req, res) => {
  try {
    // حذف جميع جلسات المستخدم
    await executeQuery(
      'DELETE FROM user_sessions WHERE user_id = ?',
      [req.user.id]
    );

    res.json({
      success: true,
      message: 'Logged out from all devices',
      message_ar: 'تم تسجيل الخروج من جميع الأجهزة'
    });

  } catch (error) {
    console.error('Logout all error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout from all devices failed',
      message_ar: 'فشل في تسجيل الخروج من جميع الأجهزة'
    });
  }
};

// الحصول على بيانات المستخدم الحالي
export const getProfile = async (req, res) => {
  try {
    const userData = {
      id: req.user.id,
      uuid: req.user.uuid,
      email: req.user.email,
      name: req.user.name,
      name_ar: req.user.name_ar,
      avatar: req.user.avatar,
      role: {
        id: req.user.role_id,
        name: req.user.role_name,
        name_ar: req.user.role_name_ar,
        permissions: req.user.permissions ? JSON.parse(req.user.permissions) : []
      },
      is_verified: req.user.is_verified,
      last_login: req.user.last_login,
      created_at: req.user.created_at
    };

    res.json({
      success: true,
      data: userData
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile',
      message_ar: 'فشل في الحصول على الملف الشخصي'
    });
  }
};

// تحديث الملف الشخصي
export const updateProfile = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { name, name_ar, avatar } = req.body;
    
    const updateData = {};
    if (name) updateData.name = name;
    if (name_ar) updateData.name_ar = name_ar;
    if (avatar) updateData.avatar = avatar;

    const updatedUser = await User.update(req.user.id, updateData);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      message_ar: 'تم تحديث الملف الشخصي بنجاح',
      data: {
        id: updatedUser.id,
        name: updatedUser.name,
        name_ar: updatedUser.name_ar,
        avatar: updatedUser.avatar
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile',
      message_ar: 'فشل في تحديث الملف الشخصي'
    });
  }
};

// تغيير كلمة المرور
export const changePassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;

    // التحقق من كلمة المرور الحالية
    const user = await User.findById(req.user.id);
    const isCurrentPasswordValid = await User.verifyPassword(currentPassword, user.password);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect',
        message_ar: 'كلمة المرور الحالية غير صحيحة'
      });
    }

    // تحديث كلمة المرور
    await User.updatePassword(req.user.id, newPassword);

    // إنهاء جميع الجلسات الأخرى (اختياري)
    const authHeader = req.headers['authorization'];
    const currentToken = authHeader && authHeader.split(' ')[1];
    const currentTokenHash = Buffer.from(currentToken).toString('base64');

    await executeQuery(
      'DELETE FROM user_sessions WHERE user_id = ? AND token_hash != ?',
      [req.user.id, currentTokenHash]
    );

    res.json({
      success: true,
      message: 'Password changed successfully',
      message_ar: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to change password',
      message_ar: 'فشل في تغيير كلمة المرور'
    });
  }
};

// التحقق من صحة التوكن
export const verifyToken = async (req, res) => {
  try {
    // إذا وصل الطلب إلى هنا، فهذا يعني أن التوكن صالح (تم التحقق منه في middleware)
    res.json({
      success: true,
      message: 'Token is valid',
      message_ar: 'الرمز صالح',
      data: {
        user_id: req.user.id,
        email: req.user.email,
        role: req.user.role_name
      }
    });

  } catch (error) {
    console.error('Verify token error:', error);
    res.status(500).json({
      success: false,
      message: 'Token verification failed',
      message_ar: 'فشل في التحقق من الرمز'
    });
  }
};
