import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';
import { fileURLToPath } from 'url';
import bcrypt from 'bcryptjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const cleanDatabase = async () => {
  try {
    console.log('🧹 تنظيف قاعدة البيانات...');
    
    const dbPath = path.join(__dirname, '..', 'database', 'security_incidents.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    // حذف البيانات التجريبية
    console.log('🗑️ حذف الحوادث الأمنية...');
    await db.run('DELETE FROM security_incidents');
    
    console.log('🗑️ حذف سجلات النشاطات...');
    await db.run('DELETE FROM activity_logs');
    
    console.log('🗑️ حذف جلسات المستخدمين...');
    await db.run('DELETE FROM user_sessions');
    
    console.log('🗑️ حذف المستخدمين التجريبيين...');
    await db.run('DELETE FROM users WHERE email != ?', ['<EMAIL>']);
    
    // تحديث كلمة مرور المدير
    console.log('👤 تحديث حساب المدير...');
    const adminPassword = 'Admin@123456';
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    
    await db.run(`
      UPDATE users 
      SET password = ?, 
          name = 'System Administrator',
          name_ar = 'مدير النظام',
          login_attempts = 0,
          locked_until = NULL,
          last_login = NULL
      WHERE email = ?
    `, [hashedPassword, '<EMAIL>']);
    
    // التأكد من صلاحيات المدير الأعلى
    await db.run(`
      UPDATE roles 
      SET permissions = ? 
      WHERE name = 'super_admin'
    `, [JSON.stringify(['*'])]);
    
    await db.close();
    
    console.log('✅ تم تنظيف قاعدة البيانات بنجاح!');
    console.log('\n📋 معلومات تسجيل الدخول:');
    console.log('البريد الإلكتروني: <EMAIL>');
    console.log('كلمة المرور: Admin@123456');
    console.log('\n🎯 النظام جاهز للاستخدام مع بيانات فارغة');
    
  } catch (error) {
    console.error('❌ خطأ في تنظيف قاعدة البيانات:', error);
    process.exit(1);
  }
};

cleanDatabase();
