import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let db = null;

// إعداد قاعدة البيانات SQLite
const initDatabase = async () => {
  try {
    const dbPath = process.env.DB_PATH || path.join(__dirname, '..', 'database', 'security_incidents.db');
    
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    // تفعيل Foreign Keys
    await db.exec('PRAGMA foreign_keys = ON');
    
    console.log('✅ SQLite database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ SQLite database connection failed:', error.message);
    return false;
  }
};

// اختبار الاتصال
const testConnection = async () => {
  try {
    if (!db) {
      await initDatabase();
    }
    
    await db.get('SELECT 1');
    console.log('✅ Database connection test successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    return false;
  }
};

// دالة مساعدة لتنفيذ الاستعلامات
const executeQuery = async (query, params = []) => {
  try {
    if (!db) {
      await initDatabase();
    }

    // تحديد نوع الاستعلام
    const queryType = query.trim().toUpperCase();
    
    if (queryType.startsWith('SELECT')) {
      // استعلام SELECT - إرجاع جميع النتائج
      return await db.all(query, params);
    } else if (queryType.startsWith('INSERT')) {
      // استعلام INSERT - إرجاع معلومات الإدراج
      const result = await db.run(query, params);
      return { insertId: result.lastID, affectedRows: result.changes };
    } else if (queryType.startsWith('UPDATE') || queryType.startsWith('DELETE')) {
      // استعلام UPDATE/DELETE - إرجاع عدد الصفوف المتأثرة
      const result = await db.run(query, params);
      return { affectedRows: result.changes };
    } else {
      // استعلامات أخرى
      return await db.run(query, params);
    }
  } catch (error) {
    console.error('Database query error:', error.message);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// دالة للحصول على صف واحد
const getOne = async (query, params = []) => {
  try {
    if (!db) {
      await initDatabase();
    }
    
    return await db.get(query, params);
  } catch (error) {
    console.error('Database get one error:', error.message);
    throw error;
  }
};

// دالة للحصول على عدة صفوف
const getAll = async (query, params = []) => {
  try {
    if (!db) {
      await initDatabase();
    }
    
    return await db.all(query, params);
  } catch (error) {
    console.error('Database get all error:', error.message);
    throw error;
  }
};

// دالة لتنفيذ معاملة (transaction)
const runTransaction = async (queries) => {
  try {
    if (!db) {
      await initDatabase();
    }

    await db.exec('BEGIN TRANSACTION');
    
    const results = [];
    for (const { query, params } of queries) {
      const result = await executeQuery(query, params);
      results.push(result);
    }
    
    await db.exec('COMMIT');
    return results;
  } catch (error) {
    await db.exec('ROLLBACK');
    console.error('Transaction error:', error.message);
    throw error;
  }
};

// دالة لإغلاق قاعدة البيانات
const closeDatabase = async () => {
  try {
    if (db) {
      await db.close();
      db = null;
      console.log('Database connection closed');
    }
  } catch (error) {
    console.error('Error closing database:', error.message);
  }
};

// تحويل استعلامات MySQL إلى SQLite
const convertMySQLToSQLite = (query) => {
  // تحويل AUTO_INCREMENT إلى AUTOINCREMENT
  query = query.replace(/AUTO_INCREMENT/gi, 'AUTOINCREMENT');
  
  // تحويل CURRENT_TIMESTAMP إلى datetime('now')
  query = query.replace(/CURRENT_TIMESTAMP/gi, "datetime('now')");
  
  // تحويل NOW() إلى datetime('now')
  query = query.replace(/NOW\(\)/gi, "datetime('now')");
  
  // تحويل BOOLEAN إلى INTEGER
  query = query.replace(/BOOLEAN/gi, 'INTEGER');
  
  // تحويل JSON إلى TEXT
  query = query.replace(/JSON/gi, 'TEXT');
  
  return query;
};

// دالة مساعدة للتوافق مع MySQL
const mysqlCompatibleQuery = async (query, params = []) => {
  const convertedQuery = convertMySQLToSQLite(query);
  return await executeQuery(convertedQuery, params);
};

export {
  initDatabase,
  testConnection,
  executeQuery,
  getOne,
  getAll,
  runTransaction,
  closeDatabase,
  mysqlCompatibleQuery,
  convertMySQLToSQLite
};
