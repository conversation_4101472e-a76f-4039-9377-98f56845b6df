import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// إعداد CORS
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// إعداد middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// مسار الصحة
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Security Incident Management API is running',
    message_ar: 'واجهة برمجة تطبيقات إدارة الحوادث الأمنية تعمل بشكل طبيعي',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    database: 'SQLite'
  });
});

// مسار معلومات API
app.get('/api', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'Security Incident Management API',
      name_ar: 'واجهة برمجة تطبيقات إدارة الحوادث الأمنية',
      version: '1.0.0',
      description: 'RESTful API for security incident management system with SQLite',
      description_ar: 'واجهة برمجة تطبيقات RESTful لنظام إدارة الحوادث الأمنية مع SQLite',
      database: 'SQLite'
    }
  });
});

// مسار تسجيل الدخول المؤقت
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // بيانات تجريبية
  const users = {
    '<EMAIL>': {
      id: 1,
      email: '<EMAIL>',
      name: 'System Administrator',
      name_ar: 'مدير النظام',
      avatar: '👨‍💼',
      role: {
        name: 'super_admin',
        name_ar: 'المدير الأعلى',
        permissions: ['*']
      }
    },
    '<EMAIL>': {
      id: 2,
      email: '<EMAIL>',
      name: 'Sarah Ahmed',
      name_ar: 'سارة أحمد',
      avatar: '👩‍💻',
      role: {
        name: 'admin',
        name_ar: 'مدير',
        permissions: ['users.read', 'users.create', 'users.update']
      }
    }
  };
  
  const user = users[email];
  
  if (user && (password === 'Admin@123456' || password === 'Password@123')) {
    res.json({
      success: true,
      message: 'Login successful',
      message_ar: 'تم تسجيل الدخول بنجاح',
      data: {
        user: user,
        token: 'fake-jwt-token-for-testing',
        expires_in: '24h'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid email or password',
      message_ar: 'بريد إلكتروني أو كلمة مرور غير صحيحة'
    });
  }
});

// مسار التحقق من التوكن
app.post('/api/auth/verify-token', (req, res) => {
  res.json({
    success: true,
    message: 'Token is valid',
    message_ar: 'الرمز صالح'
  });
});

// مسار الملف الشخصي
app.get('/api/auth/profile', (req, res) => {
  res.json({
    success: true,
    data: {
      id: 1,
      email: '<EMAIL>',
      name: 'System Administrator',
      name_ar: 'مدير النظام',
      avatar: '👨‍💼',
      role: {
        name: 'super_admin',
        name_ar: 'المدير الأعلى',
        permissions: ['*']
      }
    }
  });
});

// مسار تسجيل الخروج
app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logout successful',
    message_ar: 'تم تسجيل الخروج بنجاح'
  });
});

// مسار المستخدمين
app.get('/api/users', (req, res) => {
  res.json({
    success: true,
    data: {
      users: [
        {
          id: 1,
          email: '<EMAIL>',
          name: 'System Administrator',
          name_ar: 'مدير النظام',
          avatar: '👨‍💼',
          role_name: 'super_admin',
          role_name_ar: 'المدير الأعلى',
          is_active: true,
          is_verified: true,
          last_login: new Date().toISOString()
        },
        {
          id: 2,
          email: '<EMAIL>',
          name: 'Sarah Ahmed',
          name_ar: 'سارة أحمد',
          avatar: '👩‍💻',
          role_name: 'admin',
          role_name_ar: 'مدير',
          is_active: true,
          is_verified: true,
          last_login: new Date().toISOString()
        }
      ],
      stats: {
        total: 2,
        active: 2,
        verified: 2,
        locked: 0
      }
    }
  });
});

// مسار الأدوار
app.get('/api/roles', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 1,
        name: 'super_admin',
        name_ar: 'المدير الأعلى',
        description: 'Full system access',
        description_ar: 'صلاحية كاملة للنظام',
        user_count: 1
      },
      {
        id: 2,
        name: 'admin',
        name_ar: 'مدير',
        description: 'Administrative access',
        description_ar: 'صلاحية إدارية',
        user_count: 1
      }
    ]
  });
});

// معالج الأخطاء للمسارات غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    message_ar: 'المسار غير موجود',
    requested_url: req.originalUrl,
    method: req.method
  });
});

// بدء الخادم
app.listen(PORT, () => {
  console.log('\n🚀 Simple Security API Server Started');
  console.log(`📍 Server running on: http://localhost:${PORT}`);
  console.log(`📊 Health Check: http://localhost:${PORT}/health`);
  console.log(`📚 API Info: http://localhost:${PORT}/api`);
  console.log('\n📋 Test Login:');
  console.log('Email: <EMAIL>');
  console.log('Password: Admin@123456');
  console.log('\n✅ Server is ready to accept connections');
});
