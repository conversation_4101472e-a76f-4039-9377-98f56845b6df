import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { testConnection, closeDatabase } from './config/databaseSQLite.js';

// استيراد المسارات
import authRoutes from './routes/auth.js';
import userRoutes from './routes/users.js';
import roleRoutes from './routes/roles.js';
import incidentRoutes from './routes/incidents.js';

// تحميل متغيرات البيئة
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// إعداد الأمان
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// إعداد CORS
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://yourdomain.com'] // استبدل بالدومين الخاص بك
    : ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// إعداد middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// إعداد التسجيل
if (process.env.NODE_ENV !== 'production') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// إعداد trust proxy للحصول على IP الحقيقي
app.set('trust proxy', 1);

// مسار الصحة العامة
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Security Incident Management API is running',
    message_ar: 'واجهة برمجة تطبيقات إدارة الحوادث الأمنية تعمل بشكل طبيعي',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// مسار معلومات API
app.get('/api', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'Security Incident Management API',
      name_ar: 'واجهة برمجة تطبيقات إدارة الحوادث الأمنية',
      version: '1.0.0',
      description: 'RESTful API for security incident management system',
      description_ar: 'واجهة برمجة تطبيقات RESTful لنظام إدارة الحوادث الأمنية',
      endpoints: {
        auth: '/api/auth',
        users: '/api/users',
        roles: '/api/roles'
      },
      documentation: '/api/docs'
    }
  });
});

// تسجيل المسارات
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/incidents', incidentRoutes);

// معالج الأخطاء للمسارات غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    message_ar: 'المسار غير موجود',
    requested_url: req.originalUrl,
    method: req.method
  });
});

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);

  // أخطاء التحقق من صحة JSON
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    return res.status(400).json({
      success: false,
      message: 'Invalid JSON format',
      message_ar: 'تنسيق JSON غير صالح'
    });
  }

  // أخطاء قاعدة البيانات
  if (error.code === 'ER_DUP_ENTRY') {
    return res.status(409).json({
      success: false,
      message: 'Duplicate entry detected',
      message_ar: 'تم اكتشاف إدخال مكرر'
    });
  }

  // خطأ عام
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    message_ar: 'خطأ داخلي في الخادم',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// دالة بدء الخادم
const startServer = async () => {
  try {
    // اختبار الاتصال بقاعدة البيانات
    const dbConnected = await testConnection();

    if (!dbConnected) {
      console.error('❌ Failed to connect to database. Please check your database configuration.');
      console.log('\n📋 Database Setup Instructions:');
      console.log('1. Make sure MySQL is running');
      console.log('2. Update database credentials in .env file');
      console.log('3. Run: npm run setup-db');
      console.log('4. Run: npm run seed-db');
      process.exit(1);
    }

    // بدء الخادم
    const server = app.listen(PORT, () => {
      console.log('\n🚀 Security Incident Management API Server Started');
      console.log(`📍 Server running on: http://localhost:${PORT}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
      console.log(`📊 Health Check: http://localhost:${PORT}/health`);
      console.log(`📚 API Info: http://localhost:${PORT}/api`);
      console.log('\n📋 Available Endpoints:');
      console.log(`🔐 Authentication: http://localhost:${PORT}/api/auth`);
      console.log(`👥 Users: http://localhost:${PORT}/api/users`);
      console.log(`🎭 Roles: http://localhost:${PORT}/api/roles`);
      console.log(`🚨 Incidents: http://localhost:${PORT}/api/incidents`);
      console.log('\n✅ Server is ready to accept connections');
    });

    // معالجة إغلاق الخادم بشكل صحيح
    const gracefulShutdown = async (signal) => {
      console.log(`\n📡 Received ${signal}. Starting graceful shutdown...`);

      server.close(async () => {
        console.log('🔌 HTTP server closed');

        try {
          await closeDatabase();
          console.log('🗄️ Database connections closed');
          console.log('✅ Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          console.error('❌ Error during shutdown:', error);
          process.exit(1);
        }
      });

      // إجبار الإغلاق بعد 10 ثوانٍ
      setTimeout(() => {
        console.error('⚠️ Forcing shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    // معالجة إشارات الإغلاق
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // معالجة الأخطاء غير المعالجة
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// بدء الخادم
startServer();
