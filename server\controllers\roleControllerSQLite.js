import Role from '../models/RoleSQLite.js';
import { validationResult } from 'express-validator';

// الحصول على جميع الأدوار
export const getAllRoles = async (req, res) => {
  try {
    const roles = await Role.findAll();

    res.json({
      success: true,
      data: roles
    });

  } catch (error) {
    console.error('Get all roles error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch roles',
      message_ar: 'فشل في جلب الأدوار'
    });
  }
};

// الحصول على دور بالمعرف
export const getRoleById = async (req, res) => {
  try {
    const { id } = req.params;
    const role = await Role.findById(id);

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found',
        message_ar: 'الدور غير موجود'
      });
    }

    res.json({
      success: true,
      data: role
    });

  } catch (error) {
    console.error('Get role by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch role',
      message_ar: 'فشل في جلب الدور'
    });
  }
};

// إنشاء دور جديد
export const createRole = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { name, name_ar, description, description_ar, permissions } = req.body;

    // التحقق من عدم وجود دور بنفس الاسم
    const existingRole = await Role.findByName(name);
    if (existingRole) {
      return res.status(409).json({
        success: false,
        message: 'Role name already exists',
        message_ar: 'اسم الدور موجود بالفعل'
      });
    }

    // التحقق من صحة الصلاحيات
    if (!Role.validatePermissions(permissions)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid permissions',
        message_ar: 'صلاحيات غير صالحة'
      });
    }

    // إنشاء الدور
    const newRole = await Role.create({
      name,
      name_ar,
      description,
      description_ar,
      permissions
    });

    res.status(201).json({
      success: true,
      message: 'Role created successfully',
      message_ar: 'تم إنشاء الدور بنجاح',
      data: newRole
    });

  } catch (error) {
    console.error('Create role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create role',
      message_ar: 'فشل في إنشاء الدور'
    });
  }
};

// تحديث دور
export const updateRole = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        message_ar: 'بيانات إدخال غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { name, name_ar, description, description_ar, permissions } = req.body;

    // التحقق من وجود الدور
    const existingRole = await Role.findById(id);
    if (!existingRole) {
      return res.status(404).json({
        success: false,
        message: 'Role not found',
        message_ar: 'الدور غير موجود'
      });
    }

    // منع تعديل الأدوار الأساسية
    const protectedRoles = ['super_admin', 'admin'];
    if (protectedRoles.includes(existingRole.name)) {
      return res.status(403).json({
        success: false,
        message: 'Cannot modify system roles',
        message_ar: 'لا يمكن تعديل أدوار النظام'
      });
    }

    // التحقق من عدم تكرار الاسم (إذا تم تغييره)
    if (name && name !== existingRole.name) {
      const duplicateRole = await Role.findByName(name);
      if (duplicateRole) {
        return res.status(409).json({
          success: false,
          message: 'Role name already exists',
          message_ar: 'اسم الدور موجود بالفعل'
        });
      }
    }

    // التحقق من صحة الصلاحيات
    if (permissions && !Role.validatePermissions(permissions)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid permissions',
        message_ar: 'صلاحيات غير صالحة'
      });
    }

    // إعداد البيانات للتحديث
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (name_ar !== undefined) updateData.name_ar = name_ar;
    if (description !== undefined) updateData.description = description;
    if (description_ar !== undefined) updateData.description_ar = description_ar;
    if (permissions !== undefined) updateData.permissions = permissions;

    // تحديث الدور
    const updatedRole = await Role.update(id, updateData);

    res.json({
      success: true,
      message: 'Role updated successfully',
      message_ar: 'تم تحديث الدور بنجاح',
      data: updatedRole
    });

  } catch (error) {
    console.error('Update role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update role',
      message_ar: 'فشل في تحديث الدور'
    });
  }
};

// حذف دور
export const deleteRole = async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من وجود الدور
    const existingRole = await Role.findById(id);
    if (!existingRole) {
      return res.status(404).json({
        success: false,
        message: 'Role not found',
        message_ar: 'الدور غير موجود'
      });
    }

    // منع حذف الأدوار الأساسية
    const protectedRoles = ['super_admin', 'admin', 'security_analyst', 'viewer'];
    if (protectedRoles.includes(existingRole.name)) {
      return res.status(403).json({
        success: false,
        message: 'Cannot delete system roles',
        message_ar: 'لا يمكن حذف أدوار النظام'
      });
    }

    // حذف الدور
    const deleted = await Role.delete(id);

    if (!deleted) {
      return res.status(500).json({
        success: false,
        message: 'Failed to delete role',
        message_ar: 'فشل في حذف الدور'
      });
    }

    res.json({
      success: true,
      message: 'Role deleted successfully',
      message_ar: 'تم حذف الدور بنجاح'
    });

  } catch (error) {
    if (error.message === 'Cannot delete role with associated users') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete role with associated users',
        message_ar: 'لا يمكن حذف دور مرتبط بمستخدمين'
      });
    }

    console.error('Delete role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete role',
      message_ar: 'فشل في حذف الدور'
    });
  }
};

// الحصول على الصلاحيات المتاحة
export const getAvailablePermissions = async (req, res) => {
  try {
    const permissions = Role.getAvailablePermissions();

    res.json({
      success: true,
      data: permissions
    });

  } catch (error) {
    console.error('Get available permissions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch available permissions',
      message_ar: 'فشل في جلب الصلاحيات المتاحة'
    });
  }
};
